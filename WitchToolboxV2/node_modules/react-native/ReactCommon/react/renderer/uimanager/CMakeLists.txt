# Copyright (c) Meta Platforms, Inc. and affiliates.
#
# This source code is licensed under the MIT license found in the
# LICENSE file in the root directory of this source tree.

cmake_minimum_required(VERSION 3.13)
set(CMAKE_VERBOSE_MAKEFILE on)

add_compile_options(
        -fexceptions
        -frtti
        -std=c++20
        -Wall
        -Wpedantic
        -Wno-unused-local-typedef
        -DLOG_TAG=\"Fabric\")

file(GLOB react_renderer_uimanager_SRC CONFIGURE_DEPENDS *.cpp)
add_library(react_renderer_uimanager OBJECT ${react_renderer_uimanager_SRC})

target_include_directories(react_renderer_uimanager PUBLIC ${REACT_COMMON_DIR})

target_link_libraries(react_renderer_uimanager
        glog
        folly_runtime
        jsi
        react_cxxreact
        react_debug
        react_featureflags
        react_renderer_componentregistry
        react_renderer_consistency
        react_renderer_uimanager_consistency
        react_renderer_core
        react_renderer_debug
        react_renderer_dom
        react_renderer_graphics
        react_renderer_leakchecker
        react_renderer_runtimescheduler
        react_renderer_mounting
        rrc_root
        rrc_view
        runtimeexecutor
)
