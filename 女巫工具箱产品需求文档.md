# 女巫工具箱 - 产品需求文档 (PRD)

## 1. 产品概述

### 1.1 产品定位
女巫工具箱是一款面向身心灵爱好者的to小B移动应用，为用户提供塔罗、神谕卡、OH卡等占卜工具的学习和实践平台，同时提供成长服务和自媒体制作教学功能。

### 1.2 目标用户
- **主要用户群体**：身心灵爱好者、占卜师、塔罗师
- **次要用户群体**：对神秘学感兴趣的初学者
- **用户特征**：
  - 年龄：20-45岁，以女性为主
  - 收入：中等收入群体
  - 兴趣：身心灵成长、占卜、冥想、自我探索
  - 痛点：缺乏系统性学习资源、工具分散、缺乏实践指导

### 1.3 产品价值
- 为身心灵爱好者提供一站式学习和实践平台
- 降低占卜工具的学习门槛
- 提供专业的成长服务和变现指导
- 构建身心灵社区生态

## 2. 功能架构

### 2.1 核心功能模块

#### 2.1.1 占卜工具模块
**塔罗牌功能**
- 塔罗牌库：完整的78张塔罗牌展示
- 牌意解读：每张牌的正逆位含义详解
- 牌阵系统：
  - 单张抽牌
  - 三张牌阵（过去-现在-未来）
  - 凯尔特十字牌阵
  - 自定义牌阵
- 抽牌记录：保存历史占卜记录
- 学习模式：每日一牌学习功能

**神谕卡功能**
- 多套神谕卡牌库（天使卡、独角兽卡、女神卡等）
- 卡牌含义解读
- 每日神谕：每日推送一张神谕卡
- 冥想引导：结合卡牌的冥想音频

**OH卡功能**
- OH卡图库展示
- 随机抽取功能
- 联想练习指导
- 心理探索工具

#### 2.1.2 学习成长模块
**入门教程**
- 塔罗基础知识
- 神谕卡使用方法
- OH卡心理应用
- 占卜礼仪和注意事项

**进阶课程**
- 高级牌阵解读
- 直觉开发训练
- 能量感知练习
- 专业占卜师培养

**实践工具**
- 占卜日记功能
- 学习进度追踪
- 技能测试评估
- 证书系统

#### 2.1.3 成长服务模块
**自媒体制作教学**
- 内容创作指导
- 视频制作技巧
- 社交媒体运营
- 个人品牌建设

**变现指导**
- 占卜服务定价策略
- 客户获取方法
- 服务流程标准化
- 法律风险规避

**社区功能**
- 用户交流论坛
- 经验分享
- 问答互助
- 专家答疑

### 2.2 辅助功能模块

#### 2.2.1 用户系统
- 用户注册/登录
- 个人资料管理
- 学习进度同步
- 收藏夹功能

#### 2.2.2 内容管理
- 文章资讯
- 视频教程
- 音频冥想
- 图片素材

#### 2.2.3 工具箱
- 月相日历
- 水晶能量指南
- 脉轮平衡工具
- 冥想计时器

## 3. 技术架构

### 3.1 前端技术栈
- **框架**：React Native + Expo
- **状态管理**：Redux Toolkit
- **导航**：React Navigation
- **UI组件**：NativeBase 或 React Native Elements
- **动画**：React Native Reanimated

### 3.2 后端技术栈（Demo阶段可选）
- **数据存储**：本地存储（AsyncStorage）
- **图片资源**：本地静态资源
- **音频播放**：Expo AV

### 3.3 数据结构设计

#### 3.3.1 塔罗牌数据结构
```json
{
  "id": "major_0",
  "name": "愚者",
  "englishName": "The Fool",
  "type": "major", // major/minor
  "suit": null, // 对于小阿卡纳：wands/cups/swords/pentacles
  "number": 0,
  "image": "path/to/image",
  "meanings": {
    "upright": ["新开始", "冒险精神", "纯真"],
    "reversed": ["鲁莽", "缺乏计划", "愚蠢"]
  },
  "keywords": ["开始", "旅程", "潜力"],
  "description": "详细描述..."
}
```

#### 3.3.2 用户数据结构
```json
{
  "userId": "unique_id",
  "profile": {
    "username": "用户名",
    "avatar": "头像路径",
    "level": 1,
    "experience": 0
  },
  "progress": {
    "completedLessons": [],
    "currentCourse": "塔罗入门",
    "studyStreak": 5
  },
  "history": {
    "readings": [],
    "dailyCards": []
  }
}
```

## 4. 用户体验设计

### 4.1 设计风格
- **主题色调**：深紫色、金色、神秘蓝
- **设计风格**：神秘、优雅、现代
- **字体**：易读性强的中文字体
- **图标**：线性图标风格，符合神秘学主题

### 4.2 关键页面设计

#### 4.2.1 首页
- 每日神谕卡展示
- 快速占卜入口
- 学习进度展示
- 推荐内容

#### 4.2.2 占卜页面
- 卡牌选择界面
- 抽牌动画效果
- 结果展示页面
- 解读详情页面

#### 4.2.3 学习页面
- 课程列表
- 进度追踪
- 成就系统
- 学习日历

### 4.3 交互设计
- 卡牌翻转动画
- 抽牌手势操作
- 音效反馈
- 触觉反馈

## 5. Demo版本功能清单

### 5.1 MVP功能（第一版本）
- [ ] 基础塔罗牌库（22张大阿卡纳）
- [ ] 简单抽牌功能（单张、三张）
- [ ] 基础牌意解读
- [ ] 用户注册/登录
- [ ] 抽牌历史记录

### 5.2 Demo增强功能（第二版本）
- [ ] 完整塔罗牌库（78张）
- [ ] 神谕卡模块
- [ ] 每日一牌功能
- [ ] 基础学习教程
- [ ] 占卜日记功能

### 5.3 扩展功能（后续版本）
- [ ] OH卡模块
- [ ] 高级牌阵
- [ ] 社区功能
- [ ] 自媒体教学模块
- [ ] 变现指导功能

## 6. 开发计划

### 6.1 开发阶段
**阶段一：基础框架搭建（1-2周）**
- 项目初始化
- 基础导航结构
- UI组件库集成
- 数据结构设计

**阶段二：核心功能开发（2-3周）**
- 塔罗牌模块开发
- 抽牌功能实现
- 牌意展示功能
- 用户系统基础功能

**阶段三：功能完善（1-2周）**
- 历史记录功能
- 学习模块基础版
- UI优化和动画效果
- 测试和调试

### 6.2 技术难点
- 卡牌动画效果实现
- 随机算法设计
- 数据持久化
- 性能优化

## 7. 运营策略

### 7.1 内容策略
- 专业的占卜知识内容
- 定期更新学习资料
- 用户生成内容鼓励
- 专家入驻计划

### 7.2 用户增长
- 社交媒体推广
- KOL合作
- 免费试用策略
- 口碑传播

### 7.3 商业模式
- 免费基础功能 + 付费高级功能
- 课程付费
- 一对一咨询服务
- 周边商品销售

## 8. 风险评估

### 8.1 技术风险
- 跨平台兼容性问题
- 性能优化挑战
- 数据安全问题

### 8.2 市场风险
- 目标用户群体相对小众
- 竞品竞争激烈
- 政策法规风险

### 8.3 运营风险
- 内容质量控制
- 用户留存挑战
- 变现模式验证

## 9. 成功指标

### 9.1 用户指标
- 日活跃用户数（DAU）
- 用户留存率
- 用户使用时长
- 功能使用频率

### 9.2 业务指标
- 付费转化率
- 客单价
- 用户生命周期价值（LTV）
- 获客成本（CAC）

### 9.3 产品指标
- 功能使用率
- 用户满意度
- 应用评分
- 崩溃率和性能指标

---

**文档版本**：v1.0  
**创建日期**：2025-07-29  
**最后更新**：2025-07-29  
**负责人**：产品团队
