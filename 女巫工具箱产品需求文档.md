# 女巫工具箱 - 产品需求文档 (PRD)

## 1. 产品概述

### 1.1 产品定位
女巫工具箱是一款面向身心灵爱好者的to小B移动应用，提供全方位的占卜工具、学习教程、实践指导和商业化服务。不仅包含塔罗、神谕卡、OH卡等基础占卜工具，还涵盖数字占卜、生命密码、占星学、水晶疗愈、冥想引导等多元化身心灵服务，同时为用户提供专业的占卜师培训和变现指导。

### 1.2 目标用户
- **主要用户群体**：身心灵爱好者、占卜师、塔罗师
- **次要用户群体**：对神秘学感兴趣的初学者
- **用户特征**：
  - 年龄：20-45岁，以女性为主
  - 收入：中等收入群体
  - 兴趣：身心灵成长、占卜、冥想、自我探索
  - 痛点：缺乏系统性学习资源、工具分散、缺乏实践指导

### 1.3 产品价值
- 为身心灵爱好者提供一站式学习和实践平台
- 降低占卜工具的学习门槛
- 提供专业的成长服务和变现指导
- 构建身心灵社区生态

## 2. 功能架构

### 2.1 核心功能模块

#### 2.1.1 占卜工具模块

**塔罗牌功能**
- 塔罗牌库：完整的78张塔罗牌展示（韦特、透特、马赛等多套牌组）
- 牌意解读：每张牌的正逆位含义详解，包含爱情、事业、财运等分类解读
- 牌阵系统：
  - 单张抽牌（每日指引）
  - 三张牌阵（过去-现在-未来）
  - 凯尔特十字牌阵
  - 爱情牌阵（关系发展、复合可能性）
  - 事业牌阵（职场发展、跳槽建议）
  - 财运牌阵（投资建议、财务状况）
  - 年运牌阵（全年运势预测）
  - 自定义牌阵创建工具
- 专业占卜模式：为他人占卜的专用界面和流程
- 占卜报告生成：自动生成美观的占卜报告，可分享给客户
- 抽牌记录：保存历史占卜记录，支持标签分类
- 学习模式：每日一牌学习功能，牌意记忆训练

**神谕卡功能**
- 多套神谕卡牌库：
  - 天使卡（大天使、守护天使系列）
  - 独角兽卡（魔法能量指引）
  - 女神卡（女性力量觉醒）
  - 动物精灵卡（图腾动物指引）
  - 水晶卡（水晶能量疗愈）
  - 花精卡（巴赫花精疗愈）
  - 月亮卡（月相能量指引）
- 卡牌含义解读：深度解析每张卡的能量含义
- 每日神谕：每日推送一张神谕卡，包含冥想引导
- 神谕组合：多张卡牌组合解读
- 冥想引导：结合卡牌的冥想音频和视觉引导

**OH卡功能**
- OH卡图库展示：完整的88张图卡和88张字卡
- 随机抽取功能：支持单张、组合抽取
- 联想练习指导：心理投射技巧教学
- 心理探索工具：自我认知、情绪疗愈应用
- 团体活动模式：适合心理咨询师使用的团体工具

**数字占卜功能**
- 生命密码计算：根据生日计算生命数字
- 姓名数字学：姓名笔画数字分析
- 每日数字运势：基于生命数字的每日指引
- 数字配对分析：恋人、朋友、合作伙伴数字匹配度

**占星学功能**
- 星座运势：12星座每日、每周、每月运势
- 星盘生成：基本星盘绘制和解读
- 行星逆行提醒：水逆、火逆等行星动态
- 月相日历：新月、满月能量指引
- 星座配对：恋爱、友情、事业配对分析

**易经占卜功能**
- 六爻占卜：传统铜钱占卜方式
- 梅花易数：数字起卦方法
- 卦象解读：64卦详细解释
- 变卦分析：动爻变化解读

**水晶疗愈功能**
- 水晶百科：各种水晶的属性和功效
- 脉轮对应：七大脉轮与水晶的对应关系
- 水晶搭配：不同需求的水晶组合建议
- 净化充能：水晶保养和能量激活方法
- 水晶冥想：结合水晶的冥想练习

#### 2.1.2 学习成长模块

**基础入门教程**
- 塔罗基础知识：牌组历史、基本含义、抽牌方法
- 神谕卡使用方法：不同卡组的特点和使用场景
- OH卡心理应用：投射技巧、心理分析方法
- 占卜礼仪和注意事项：职业道德、能量保护
- 数字占卜入门：生命密码计算和基础解读
- 占星学基础：星座、行星、宫位基本概念
- 易经入门：阴阳五行、八卦基础知识
- 水晶疗愈基础：水晶分类、基本使用方法

**进阶专业课程**
- 高级牌阵解读：复杂牌阵的深度分析技巧
- 直觉开发训练：第六感培养、灵感接收方法
- 能量感知练习：气场感知、能量清理技巧
- 专业占卜师培养：咨询技巧、客户沟通艺术
- 心理占卜结合：心理学在占卜中的应用
- 商业占卜技能：定价策略、服务流程标准化
- 在线占卜技巧：远程占卜的特殊方法和注意事项

**实践工具系统**
- 占卜日记功能：记录每次占卜的详细信息和后续验证
- 学习进度追踪：课程完成度、技能掌握程度可视化
- 技能测试评估：定期测试，评估学习效果
- 证书系统：完成课程后获得专业认证
- 练习模式：模拟真实占卜场景的练习环境
- 导师指导：一对一在线指导和答疑
- 同学互动：学习小组、作业互评、经验分享

**专业技能培训**
- 占卜师职业规划：从兴趣到职业的转换路径
- 客户服务技巧：如何处理不同类型的客户需求
- 危机处理能力：面对负面预测时的沟通技巧
- 法律风险规避：占卜行业的法律边界和注意事项
- 心理健康维护：占卜师的自我保护和心理调节

#### 2.1.3 成长服务模块

**自媒体制作教学**
- 内容创作指导：
  - 占卜类短视频脚本写作
  - 图文内容设计技巧
  - 直播占卜技巧和话术
  - 内容日历规划和管理
- 视频制作技巧：
  - 手机拍摄技巧和设备推荐
  - 视频剪辑软件使用教程
  - 特效和转场效果制作
  - 音频处理和背景音乐选择
- 社交媒体运营：
  - 抖音、小红书、微博等平台特点
  - 算法机制和流量获取技巧
  - 粉丝互动和社群运营
  - 跨平台内容分发策略
- 个人品牌建设：
  - 占卜师人设打造
  - 视觉形象设计指导
  - 个人故事包装技巧
  - 专业形象塑造方法

**商业变现指导**
- 占卜服务定价策略：
  - 不同服务类型的定价参考
  - 市场调研和竞品分析方法
  - 价格梯度设计和套餐组合
  - 促销活动策划技巧
- 客户获取方法：
  - 线上引流技巧和渠道建设
  - 客户画像分析和精准营销
  - 口碑营销和转介绍机制
  - 合作伙伴开发和维护
- 服务流程标准化：
  - 咨询前准备工作清单
  - 占卜过程标准化流程
  - 售后服务和客户维护
  - 服务质量控制和改进
- 多元化收入模式：
  - 一对一占卜咨询
  - 团体占卜活动组织
  - 占卜课程和工作坊
  - 周边产品销售（塔罗牌、水晶等）
  - 占卜师培训和认证

**专业工具支持**
- 客户管理系统：客户信息记录、服务历史追踪
- 预约管理工具：在线预约、时间管理、提醒功能
- 收费管理：价格计算、支付记录、财务统计
- 占卜报告模板：专业报告生成、个性化定制
- 营销素材库：海报模板、文案素材、视频模板

**社区生态建设**
- 用户交流论坛：
  - 经验分享版块
  - 疑难问题讨论
  - 占卜案例分析
  - 行业动态交流
- 专业互助平台：
  - 新手导师配对
  - 技能交换和合作
  - 资源共享和推荐
  - 行业人脉建设
- 专家答疑系统：
  - 定期专家直播答疑
  - 一对一专业指导
  - 疑难案例会诊
  - 职业发展咨询

### 2.2 辅助功能模块

#### 2.2.1 用户系统
- 用户注册/登录
- 个人资料管理
- 学习进度同步
- 收藏夹功能

#### 2.2.2 内容管理
- 文章资讯
- 视频教程
- 音频冥想
- 图片素材

#### 2.2.3 实用工具箱
- 月相日历：
  - 月相变化可视化展示
  - 每个月相的能量特点和适合活动
  - 月相许愿和释放仪式指导
  - 月相与占卜最佳时机提醒
- 脉轮平衡工具：
  - 七大脉轮状态检测
  - 脉轮冥想音频和引导
  - 脉轮对应颜色和水晶推荐
  - 脉轮平衡练习和瑜伽动作
- 冥想计时器：
  - 多种冥想音乐和自然音效
  - 可自定义时长和提醒间隔
  - 冥想记录和进度追踪
  - 引导冥想音频库
- 能量清理工具：
  - 空间净化仪式指导
  - 个人能量保护方法
  - 负能量清除技巧
  - 能量提升和充电方法
- 数字能量计算器：
  - 手机号码能量分析
  - 车牌号码吉凶测算
  - 门牌号码能量解读
  - 重要日期数字分析
- 颜色疗愈工具：
  - 每日幸运颜色推荐
  - 颜色心理学应用
  - 服装搭配建议
  - 家居颜色风水指导
- 香薰疗愈指南：
  - 不同精油的功效和使用方法
  - 情绪对应的香薰配方
  - 冥想和占卜专用香薰
  - DIY香薰制作教程
- 仪式工具包：
  - 满月仪式流程和用品
  - 新月许愿仪式指导
  - 能量净化仪式步骤
  - 个人成长仪式设计

## 3. 技术架构

### 3.1 前端技术栈
- **框架**：React Native + Expo
- **状态管理**：Redux Toolkit
- **导航**：React Navigation
- **UI组件**：NativeBase 或 React Native Elements
- **动画**：React Native Reanimated

### 3.2 后端技术栈（Demo阶段可选）
- **数据存储**：本地存储（AsyncStorage）
- **图片资源**：本地静态资源
- **音频播放**：Expo AV

### 3.3 数据结构设计

#### 3.3.1 塔罗牌数据结构
```json
{
  "id": "major_0",
  "name": "愚者",
  "englishName": "The Fool",
  "type": "major", // major/minor
  "suit": null, // 对于小阿卡纳：wands/cups/swords/pentacles
  "number": 0,
  "image": "path/to/image",
  "meanings": {
    "upright": ["新开始", "冒险精神", "纯真"],
    "reversed": ["鲁莽", "缺乏计划", "愚蠢"]
  },
  "keywords": ["开始", "旅程", "潜力"],
  "description": "详细描述..."
}
```

#### 3.3.2 用户数据结构
```json
{
  "userId": "unique_id",
  "profile": {
    "username": "用户名",
    "avatar": "头像路径",
    "level": 1,
    "experience": 0
  },
  "progress": {
    "completedLessons": [],
    "currentCourse": "塔罗入门",
    "studyStreak": 5
  },
  "history": {
    "readings": [],
    "dailyCards": []
  }
}
```

## 4. 用户体验设计

### 4.1 设计风格要求

**整体设计理念**
- **设计风格**：类iOS风格，现代简约与神秘优雅相结合
- **视觉层次**：清晰的信息架构，避免过于素雅，要有视觉冲击力
- **用户体验**：流畅的交互动画，直观的操作逻辑

**色彩系统**
- **主色调**：
  - 深紫色 (#6B46C1) - 神秘、智慧
  - 金色 (#F59E0B) - 高贵、能量
  - 神秘蓝 (#1E40AF) - 深邃、宁静
- **辅助色彩**：
  - 玫瑰金 (#E879F9) - 女性化、温暖
  - 翡翠绿 (#10B981) - 自然、疗愈
  - 珍珠白 (#F8FAFC) - 纯净、平衡
- **渐变效果**：大量使用渐变色彩，营造梦幻神秘氛围
- **暗色模式**：提供深色主题，适合夜晚使用

**字体系统**
- **中文字体**：苹方 (PingFang SC) / 思源黑体
- **英文字体**：SF Pro Display / Helvetica Neue
- **装饰字体**：用于标题和特殊场景的艺术字体
- **字体层级**：清晰的字体大小和权重层级

**图标设计**
- **风格**：线性图标为主，结合面性图标
- **主题元素**：月亮、星星、水晶、符文等神秘学符号
- **动效图标**：重要功能使用动态图标增强视觉效果
- **一致性**：统一的图标风格和尺寸规范

**视觉元素**
- **卡片设计**：圆角卡片，带有阴影和光效
- **按钮样式**：渐变背景，圆角设计，点击反馈
- **分割线**：使用渐变线条或装饰性分割元素
- **背景纹理**：星空、云雾、光晕等神秘背景效果

### 4.2 关键页面设计

#### 4.2.1 启动页面
- 品牌Logo动画展示
- 星空背景动态效果
- 优雅的加载动画
- 每日一句神秘学格言

#### 4.2.2 首页设计
- **顶部区域**：
  - 个性化问候语和用户头像
  - 今日运势简要展示
  - 消息通知和设置入口
- **核心功能区**：
  - 每日神谕卡大卡片展示（带动画效果）
  - 快速占卜入口（塔罗、神谕卡、数字占卜）
  - 月相显示和能量提醒
- **学习进度区**：
  - 当前学习课程进度条
  - 学习连续天数展示
  - 今日学习任务提醒
- **推荐内容区**：
  - 精选文章和视频推荐
  - 热门占卜师动态
  - 社区热门话题

#### 4.2.3 占卜页面设计
- **选择界面**：
  - 占卜类型选择（卡片式布局）
  - 问题输入框（支持语音输入）
  - 占卜目的分类选择
- **抽牌界面**：
  - 3D卡牌展示效果
  - 手势滑动抽牌动画
  - 背景音效和震动反馈
  - 抽牌过程的仪式感营造
- **结果展示**：
  - 卡牌翻转动画效果
  - 渐进式内容展示
  - 美观的排版和配色
  - 分享功能和保存选项
- **专业占卜模式**：
  - 客户信息录入界面
  - 占卜过程记录工具
  - 专业报告生成页面
  - 客户管理功能

#### 4.2.4 学习页面设计
- **课程首页**：
  - 学习路径可视化展示
  - 课程分类和难度标识
  - 学习进度和成就展示
- **课程详情**：
  - 视频播放器（支持倍速、字幕）
  - 互动练习和测试
  - 笔记记录功能
  - 讨论区和问答
- **练习模式**：
  - 模拟占卜环境
  - 实时反馈和指导
  - 错误纠正和建议
  - 练习记录和分析

#### 4.2.5 工具箱页面
- **工具分类**：清晰的分类导航
- **工具卡片**：每个工具的功能预览
- **快速访问**：常用工具的快捷入口
- **个性化推荐**：基于使用习惯的工具推荐

### 4.3 交互设计规范

**动画效果**
- **页面转场**：流畅的页面切换动画，符合iOS设计规范
- **卡牌动画**：
  - 3D翻转效果
  - 抽牌时的飞入动画
  - 卡牌悬浮和光效
  - 洗牌动画效果
- **加载动画**：
  - 星空粒子效果
  - 水晶旋转动画
  - 能量波纹扩散
  - 渐变色彩变化

**手势操作**
- **滑动操作**：左右滑动切换卡牌或页面
- **长按操作**：长按查看详细信息或快捷菜单
- **双击操作**：双击收藏或点赞
- **捏合操作**：图片缩放和卡牌详情查看

**反馈机制**
- **视觉反馈**：按钮点击状态变化、选中高亮效果
- **音效反馈**：
  - 卡牌翻转音效
  - 成功操作提示音
  - 背景环境音乐
  - 冥想引导音频
- **触觉反馈**：
  - 重要操作的震动反馈
  - 抽牌时的触觉模拟
  - 成就解锁的震动提醒

**无障碍设计**
- **字体大小**：支持系统字体大小调节
- **颜色对比**：确保足够的颜色对比度
- **语音支持**：重要功能支持语音操作
- **简化模式**：为视力不佳用户提供简化界面

## 5. Demo版本功能清单

### 5.1 Demo完整功能清单

**注意：我们要一次性实现所有核心功能，打造完整的产品体验**

#### 5.1.1 占卜工具模块（全功能实现）
- [ ] **塔罗牌系统**
  - [ ] 完整78张塔罗牌库（韦特牌）
  - [ ] 多种牌阵（单张、三张、凯尔特十字、爱情、事业、财运）
  - [ ] 专业占卜模式（为他人占卜）
  - [ ] 占卜报告生成和分享
  - [ ] 抽牌历史记录和分类
  - [ ] 每日一牌学习功能

- [ ] **神谕卡系统**
  - [ ] 5套神谕卡牌库（天使卡、独角兽卡、女神卡、动物精灵卡、水晶卡）
  - [ ] 每日神谕推送
  - [ ] 神谕组合解读
  - [ ] 冥想引导音频

- [ ] **OH卡系统**
  - [ ] 完整OH卡库（88张图卡+88张字卡）
  - [ ] 心理投射练习
  - [ ] 团体活动模式

- [ ] **数字占卜系统**
  - [ ] 生命密码计算和解读
  - [ ] 姓名数字学分析
  - [ ] 每日数字运势
  - [ ] 数字配对分析

- [ ] **占星学系统**
  - [ ] 12星座运势（每日/每周/每月）
  - [ ] 基础星盘生成
  - [ ] 行星逆行提醒
  - [ ] 月相日历和能量指引
  - [ ] 星座配对分析

- [ ] **易经占卜系统**
  - [ ] 六爻占卜功能
  - [ ] 64卦象解读
  - [ ] 变卦分析

- [ ] **水晶疗愈系统**
  - [ ] 水晶百科（50+种水晶）
  - [ ] 脉轮对应指导
  - [ ] 水晶搭配建议
  - [ ] 净化充能方法

#### 5.1.2 学习成长模块（全功能实现）
- [ ] **基础教程系统**
  - [ ] 8个基础入门课程（塔罗、神谕卡、OH卡、数字占卜、占星、易经、水晶、占卜礼仪）
  - [ ] 视频教程播放器
  - [ ] 互动练习和测试
  - [ ] 学习笔记功能

- [ ] **进阶课程系统**
  - [ ] 7个专业进阶课程
  - [ ] 实践练习模式
  - [ ] 技能评估系统
  - [ ] 证书颁发功能

- [ ] **学习工具**
  - [ ] 占卜日记功能
  - [ ] 学习进度追踪
  - [ ] 每日学习任务
  - [ ] 学习成就系统

#### 5.1.3 成长服务模块（全功能实现）
- [ ] **自媒体教学系统**
  - [ ] 内容创作指导课程
  - [ ] 视频制作技巧教程
  - [ ] 社交媒体运营指南
  - [ ] 个人品牌建设课程

- [ ] **商业变现系统**
  - [ ] 定价策略指导
  - [ ] 客户获取方法
  - [ ] 服务流程标准化
  - [ ] 多元化收入模式介绍

- [ ] **专业工具支持**
  - [ ] 客户管理系统
  - [ ] 预约管理工具
  - [ ] 收费管理功能
  - [ ] 营销素材库

- [ ] **社区功能**
  - [ ] 用户交流论坛
  - [ ] 经验分享版块
  - [ ] 专家答疑系统
  - [ ] 学习小组功能

#### 5.1.4 实用工具箱（全功能实现）
- [ ] **能量工具**
  - [ ] 月相日历
  - [ ] 脉轮平衡工具
  - [ ] 能量清理指导
  - [ ] 冥想计时器

- [ ] **分析工具**
  - [ ] 数字能量计算器
  - [ ] 颜色疗愈工具
  - [ ] 香薰疗愈指南
  - [ ] 仪式工具包

#### 5.1.5 用户系统（全功能实现）
- [ ] **账户管理**
  - [ ] 用户注册/登录
  - [ ] 个人资料管理
  - [ ] 头像和个性化设置
  - [ ] 隐私设置

- [ ] **数据同步**
  - [ ] 学习进度同步
  - [ ] 占卜记录云存储
  - [ ] 收藏夹功能
  - [ ] 跨设备数据同步

#### 5.1.6 内容管理（全功能实现）
- [ ] **内容库**
  - [ ] 文章资讯系统
  - [ ] 视频教程库
  - [ ] 音频冥想库
  - [ ] 图片素材库

- [ ] **推荐系统**
  - [ ] 个性化内容推荐
  - [ ] 学习路径推荐
  - [ ] 工具使用建议
  - [ ] 每日精选内容

### 5.2 技术实现重点
- [ ] **UI/UX实现**
  - [ ] 类iOS设计风格实现
  - [ ] 流畅的动画效果
  - [ ] 美观的视觉设计
  - [ ] 直观的交互体验

- [ ] **性能优化**
  - [ ] 图片资源优化
  - [ ] 动画性能优化
  - [ ] 内存管理
  - [ ] 启动速度优化

- [ ] **数据管理**
  - [ ] 本地数据存储
  - [ ] 数据结构设计
  - [ ] 缓存机制
  - [ ] 数据备份恢复

## 6. 开发计划

### 6.1 开发阶段（重新规划）

**阶段一：项目基础和UI框架（2-3周）**
- React Native + Expo项目初始化
- 类iOS设计系统搭建
- 基础组件库开发（按钮、卡片、导航等）
- 主要页面框架和导航结构
- 色彩系统和主题配置
- 基础动画库集成

**阶段二：占卜工具核心功能（3-4周）**
- 塔罗牌系统完整实现（78张牌+多种牌阵）
- 神谕卡系统实现（5套卡牌）
- OH卡系统实现
- 数字占卜功能实现
- 占星学基础功能
- 易经占卜功能
- 专业占卜模式和报告生成

**阶段三：学习和成长模块（2-3周）**
- 完整教程系统实现
- 视频播放器和学习工具
- 学习进度追踪系统
- 占卜日记和记录功能
- 自媒体教学模块
- 商业变现指导功能

**阶段四：工具箱和社区功能（2周）**
- 实用工具箱全部功能
- 社区论坛和交流功能
- 专家答疑系统
- 客户管理和预约系统
- 营销工具和素材库

**阶段五：优化和完善（1-2周）**
- UI/UX细节优化
- 动画效果完善
- 性能优化和测试
- 数据管理和同步
- 最终调试和发布准备

**总开发周期：10-14周（约3-3.5个月）**

### 6.2 技术难点和解决方案

**UI/UX实现难点**
- **3D卡牌动画效果**：使用React Native Reanimated 3实现流畅的3D翻转和抽牌动画
- **复杂渐变和视觉效果**：结合LinearGradient和Svg组件实现神秘学风格的视觉效果
- **类iOS设计还原**：严格按照iOS设计规范，使用原生组件样式
- **动画性能优化**：使用原生驱动动画，避免JS线程阻塞

**功能实现难点**
- **多套卡牌系统管理**：设计统一的卡牌数据结构和管理系统
- **复杂牌阵算法**：实现各种塔罗牌阵的位置计算和解读逻辑
- **占星学计算**：集成占星学计算库，实现星盘生成和行星位置计算
- **音频播放管理**：使用Expo AV实现背景音乐和音效的无缝播放

**数据管理难点**
- **大量静态资源管理**：优化图片资源，使用合适的格式和尺寸
- **本地数据存储**：使用AsyncStorage和SQLite结合，实现复杂数据的本地存储
- **数据同步机制**：设计离线优先的数据同步策略
- **缓存策略**：实现智能缓存，提升应用响应速度

**性能优化难点**
- **内存管理**：大量图片资源的内存优化和释放
- **启动速度**：优化应用启动时间，实现快速冷启动
- **滚动性能**：长列表和复杂界面的滚动性能优化
- **包体积控制**：压缩资源文件，控制APK/IPA大小

## 7. 运营策略

### 7.1 内容策略
- 专业的占卜知识内容
- 定期更新学习资料
- 用户生成内容鼓励
- 专家入驻计划

### 7.2 用户增长
- 社交媒体推广
- KOL合作
- 免费试用策略
- 口碑传播

### 7.3 商业模式
- 免费基础功能 + 付费高级功能
- 课程付费
- 一对一咨询服务
- 周边商品销售

## 8. 风险评估

### 8.1 技术风险
- 跨平台兼容性问题
- 性能优化挑战
- 数据安全问题

### 8.2 市场风险
- 目标用户群体相对小众
- 竞品竞争激烈
- 政策法规风险

### 8.3 运营风险
- 内容质量控制
- 用户留存挑战
- 变现模式验证

## 9. 成功指标

### 9.1 用户指标
- 日活跃用户数（DAU）
- 用户留存率
- 用户使用时长
- 功能使用频率

### 9.2 业务指标
- 付费转化率
- 客单价
- 用户生命周期价值（LTV）
- 获客成本（CAC）

### 9.3 产品指标
- 功能使用率
- 用户满意度
- 应用评分
- 崩溃率和性能指标

---

**文档版本**：v1.0  
**创建日期**：2025-07-29  
**最后更新**：2025-07-29  
**负责人**：产品团队
