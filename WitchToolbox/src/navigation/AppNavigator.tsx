import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';
import { useAppSelector } from '../store';
import { Colors } from '../constants/colors';

// Import screens
import HomeScreen from '../screens/home/<USER>';
import DivinationScreen from '../screens/divination/DivinationScreen';
import LearningScreen from '../screens/learning/LearningScreen';
import ToolsScreen from '../screens/tools/ToolsScreen';
import ProfileScreen from '../screens/profile/ProfileScreen';

// Import detailed screens
import TarotReadingScreen from '../screens/divination/TarotReadingScreen';
import OracleReadingScreen from '../screens/divination/OracleReadingScreen';
import CourseDetailScreen from '../screens/learning/CourseDetailScreen';
import LessonDetailScreen from '../screens/learning/LessonDetailScreen';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

// Tab Navigator
function TabNavigator() {
  const theme = useAppSelector(state => state.app.theme);
  const isDark = theme === 'dark';

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          switch (route.name) {
            case 'Home':
              iconName = focused ? 'home' : 'home-outline';
              break;
            case 'Divination':
              iconName = focused ? 'sparkles' : 'sparkles-outline';
              break;
            case 'Learning':
              iconName = focused ? 'school' : 'school-outline';
              break;
            case 'Tools':
              iconName = focused ? 'construct' : 'construct-outline';
              break;
            case 'Profile':
              iconName = focused ? 'person' : 'person-outline';
              break;
            default:
              iconName = 'help-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: Colors.primary.purple,
        tabBarInactiveTintColor: isDark ? Colors.dark.textSecondary : Colors.light.textSecondary,
        tabBarStyle: {
          backgroundColor: isDark ? Colors.dark.surface : Colors.light.surface,
          borderTopColor: isDark ? Colors.dark.border : Colors.light.border,
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
        },
        headerStyle: {
          backgroundColor: isDark ? Colors.dark.surface : Colors.light.surface,
          shadowColor: 'transparent',
          elevation: 0,
        },
        headerTintColor: isDark ? Colors.dark.text : Colors.light.text,
        headerTitleStyle: {
          fontWeight: '600',
          fontSize: 18,
        },
      })}
    >
      <Tab.Screen 
        name="Home" 
        component={HomeScreen} 
        options={{ 
          title: '首页',
          headerShown: false,
        }} 
      />
      <Tab.Screen 
        name="Divination" 
        component={DivinationScreen} 
        options={{ 
          title: '占卜',
          headerTitle: '占卜工具',
        }} 
      />
      <Tab.Screen 
        name="Learning" 
        component={LearningScreen} 
        options={{ 
          title: '学习',
          headerTitle: '学习成长',
        }} 
      />
      <Tab.Screen 
        name="Tools" 
        component={ToolsScreen} 
        options={{ 
          title: '工具',
          headerTitle: '实用工具',
        }} 
      />
      <Tab.Screen 
        name="Profile" 
        component={ProfileScreen} 
        options={{ 
          title: '我的',
          headerTitle: '个人中心',
        }} 
      />
    </Tab.Navigator>
  );
}

// Main App Navigator
export default function AppNavigator() {
  const theme = useAppSelector(state => state.app.theme);
  const isDark = theme === 'dark';

  return (
    <NavigationContainer
      theme={{
        dark: isDark,
        colors: {
          primary: Colors.primary.purple,
          background: isDark ? Colors.dark.background : Colors.light.background,
          card: isDark ? Colors.dark.surface : Colors.light.surface,
          text: isDark ? Colors.dark.text : Colors.light.text,
          border: isDark ? Colors.dark.border : Colors.light.border,
          notification: Colors.functional.info,
        },
      }}
    >
      <Stack.Navigator
        screenOptions={{
          headerStyle: {
            backgroundColor: isDark ? Colors.dark.surface : Colors.light.surface,
            shadowColor: 'transparent',
            elevation: 0,
          },
          headerTintColor: isDark ? Colors.dark.text : Colors.light.text,
          headerTitleStyle: {
            fontWeight: '600',
            fontSize: 18,
          },
          headerBackTitleVisible: false,
        }}
      >
        <Stack.Screen 
          name="MainTabs" 
          component={TabNavigator} 
          options={{ headerShown: false }} 
        />
        
        {/* Divination Screens */}
        <Stack.Screen 
          name="TarotReading" 
          component={TarotReadingScreen}
          options={{ 
            title: '塔罗占卜',
            presentation: 'modal',
          }} 
        />
        <Stack.Screen 
          name="OracleReading" 
          component={OracleReadingScreen}
          options={{ 
            title: '神谕卡占卜',
            presentation: 'modal',
          }} 
        />
        
        {/* Learning Screens */}
        <Stack.Screen 
          name="CourseDetail" 
          component={CourseDetailScreen}
          options={{ 
            title: '课程详情',
          }} 
        />
        <Stack.Screen 
          name="LessonDetail" 
          component={LessonDetailScreen}
          options={{ 
            title: '课程学习',
          }} 
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}
