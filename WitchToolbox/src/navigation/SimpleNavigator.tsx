import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';

// 简化的屏幕组件
import SimpleHomeScreen from '../screens/SimpleHomeScreen';
import SimpleDivinationScreen from '../screens/SimpleDivinationScreen';
import SimpleTarotScreen from '../screens/SimpleTarotScreen';
import SimpleOracleScreen from '../screens/SimpleOracleScreen';
import TestScreen from '../screens/TestScreen';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

// Tab Navigator
function TabNavigator() {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          switch (route.name) {
            case 'Home':
              iconName = focused ? 'home' : 'home-outline';
              break;
            case 'Divination':
              iconName = focused ? 'sparkles' : 'sparkles-outline';
              break;
            case 'Test':
              iconName = focused ? 'checkmark-circle' : 'checkmark-circle-outline';
              break;
            default:
              iconName = 'help-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: Colors.primary.purple,
        tabBarInactiveTintColor: Colors.neutral.gray500,
        headerStyle: {
          backgroundColor: Colors.light.surface,
        },
        headerTintColor: Colors.light.text,
        headerTitleStyle: {
          fontWeight: '600',
          fontSize: 18,
        },
      })}
    >
      <Tab.Screen
        name="Home"
        component={SimpleHomeScreen}
        options={{
          title: '首页',
          headerTitle: '女巫工具箱',
        }}
      />
      <Tab.Screen
        name="Divination"
        component={SimpleDivinationScreen}
        options={{
          title: '占卜',
          headerTitle: '占卜工具',
        }}
      />
      <Tab.Screen
        name="Test"
        component={TestScreen}
        options={{
          title: '测试',
          headerTitle: '功能测试',
        }}
      />
    </Tab.Navigator>
  );
}

// Main Navigator
export default function SimpleNavigator() {
  return (
    <NavigationContainer>
      <Stack.Navigator
        screenOptions={{
          headerStyle: {
            backgroundColor: Colors.light.surface,
          },
          headerTintColor: Colors.light.text,
          headerTitleStyle: {
            fontWeight: '600',
            fontSize: 18,
          },
          headerBackTitleVisible: false,
        }}
      >
        <Stack.Screen
          name="MainTabs"
          component={TabNavigator}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="TarotReading"
          component={SimpleTarotScreen}
          options={{
            title: '塔罗占卜',
            presentation: 'modal',
          }}
        />
        <Stack.Screen
          name="OracleReading"
          component={SimpleOracleScreen}
          options={{
            title: '神谕卡占卜',
            presentation: 'modal',
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}
