// 基础类型定义

// 塔罗牌相关类型
export interface TarotCard {
  id: string;
  name: string;
  englishName: string;
  type: 'major' | 'minor';
  suit?: 'wands' | 'cups' | 'swords' | 'pentacles';
  number: number;
  image: string;
  meanings: {
    upright: string[];
    reversed: string[];
  };
  keywords: string[];
  description: string;
  element?: string;
  astrology?: string;
}

// 神谕卡类型
export interface OracleCard {
  id: string;
  name: string;
  deck: string;
  image: string;
  meaning: string;
  guidance: string;
  affirmation: string;
  keywords: string[];
}

// OH卡类型
export interface OHCard {
  id: string;
  type: 'image' | 'word';
  content: string;
  image?: string;
  category: string;
}

// 占卜记录类型
export interface DivinationReading {
  id: string;
  type: 'tarot' | 'oracle' | 'oh' | 'numerology' | 'astrology';
  date: string;
  question: string;
  cards: (TarotCard | OracleCard | OHCard)[];
  interpretation: string;
  spread: string;
  tags: string[];
  clientName?: string;
  isForOthers: boolean;
}

// 用户类型
export interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  level: number;
  experience: number;
  joinDate: string;
  preferences: UserPreferences;
}

export interface UserPreferences {
  theme: 'light' | 'dark';
  notifications: boolean;
  soundEffects: boolean;
  hapticFeedback: boolean;
  defaultTarotDeck: string;
  favoriteOracleDeck: string;
}

// 学习相关类型
export interface Course {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  duration: number; // 分钟
  lessons: Lesson[];
  prerequisites: string[];
  certificate: boolean;
}

export interface Lesson {
  id: string;
  title: string;
  content: string;
  type: 'video' | 'text' | 'interactive';
  duration: number;
  completed: boolean;
  resources: Resource[];
}

export interface Resource {
  id: string;
  type: 'pdf' | 'audio' | 'image' | 'link';
  title: string;
  url: string;
}

// 学习进度类型
export interface LearningProgress {
  userId: string;
  courseId: string;
  completedLessons: string[];
  currentLesson: string;
  progress: number; // 0-100
  startDate: string;
  lastAccessDate: string;
  timeSpent: number; // 分钟
}

// 占星学相关类型
export interface ZodiacSign {
  id: string;
  name: string;
  symbol: string;
  element: 'fire' | 'earth' | 'air' | 'water';
  quality: 'cardinal' | 'fixed' | 'mutable';
  rulingPlanet: string;
  dateRange: string;
  traits: string[];
}

export interface Horoscope {
  sign: string;
  date: string;
  period: 'daily' | 'weekly' | 'monthly';
  content: string;
  luckyNumbers: number[];
  luckyColors: string[];
  compatibility: string[];
}

// 数字占卜类型
export interface NumerologyReading {
  lifePathNumber: number;
  destinyNumber: number;
  soulUrgeNumber: number;
  personalityNumber: number;
  birthDayNumber: number;
  interpretation: {
    [key: string]: string;
  };
}

// 水晶相关类型
export interface Crystal {
  id: string;
  name: string;
  color: string;
  chakra: string[];
  properties: string[];
  uses: string[];
  cleansing: string[];
  image: string;
  hardness: number;
  formation: string;
}

// 月相类型
export interface MoonPhase {
  date: string;
  phase: 'new' | 'waxing_crescent' | 'first_quarter' | 'waxing_gibbous' | 'full' | 'waning_gibbous' | 'last_quarter' | 'waning_crescent';
  illumination: number;
  energy: string;
  activities: string[];
  rituals: string[];
}

// 导航相关类型
export type RootStackParamList = {
  Home: undefined;
  Divination: undefined;
  Learning: undefined;
  Tools: undefined;
  Profile: undefined;
  TarotReading: { spread?: string };
  OracleReading: { deck?: string };
  CourseDetail: { courseId: string };
  LessonDetail: { lessonId: string };
};

// Redux状态类型
export interface RootState {
  user: UserState;
  divination: DivinationState;
  learning: LearningState;
  app: AppState;
}

export interface UserState {
  currentUser: User | null;
  isAuthenticated: boolean;
  preferences: UserPreferences;
}

export interface DivinationState {
  currentReading: DivinationReading | null;
  history: DivinationReading[];
  selectedCards: (TarotCard | OracleCard)[];
  isReading: boolean;
}

export interface LearningState {
  courses: Course[];
  currentCourse: Course | null;
  progress: LearningProgress[];
  achievements: Achievement[];
}

export interface AppState {
  isLoading: boolean;
  theme: 'light' | 'dark';
  notifications: Notification[];
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  unlockedAt?: string;
  progress: number;
  maxProgress: number;
}

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  timestamp: string;
  read: boolean;
}

// 工具相关类型
export interface Tool {
  id: string;
  name: string;
  description: string;
  category: string;
  icon: string;
  component: string;
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}
