import React, { useState } from 'react';
import {
  View,
  TextInput,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  TextInputProps,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../../constants/colors';
import { Spacing, FontSizes, BorderRadius, InputSizes } from '../../constants/dimensions';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  hint?: string;
  leftIcon?: keyof typeof Ionicons.glyphMap;
  rightIcon?: keyof typeof Ionicons.glyphMap;
  onRightIconPress?: () => void;
  size?: 'small' | 'medium' | 'large';
  variant?: 'default' | 'outlined' | 'filled';
  theme?: 'light' | 'dark';
  containerStyle?: ViewStyle;
  inputStyle?: TextStyle;
  labelStyle?: TextStyle;
  required?: boolean;
}

export default function Input({
  label,
  error,
  hint,
  leftIcon,
  rightIcon,
  onRightIconPress,
  size = 'medium',
  variant = 'outlined',
  theme = 'light',
  containerStyle,
  inputStyle,
  labelStyle,
  required = false,
  ...textInputProps
}: InputProps) {
  const [isFocused, setIsFocused] = useState(false);
  const isDark = theme === 'dark';
  const inputSize = InputSizes[size];

  const getContainerStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      height: inputSize.height,
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: inputSize.paddingHorizontal,
      borderRadius: BorderRadius.input,
    };

    const focusedColor = Colors.primary.purple;
    const borderColor = error 
      ? Colors.functional.error
      : isFocused 
        ? focusedColor
        : isDark ? Colors.dark.border : Colors.light.border;

    switch (variant) {
      case 'outlined':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
          borderWidth: 2,
          borderColor,
        };
      case 'filled':
        return {
          ...baseStyle,
          backgroundColor: isDark ? Colors.dark.surface : Colors.light.surface,
          borderWidth: 1,
          borderColor: 'transparent',
        };
      default:
        return {
          ...baseStyle,
          backgroundColor: isDark ? Colors.dark.card : Colors.light.card,
          borderWidth: 1,
          borderColor,
        };
    }
  };

  const getInputStyle = (): TextStyle => {
    return {
      flex: 1,
      fontSize: inputSize.fontSize,
      color: isDark ? Colors.dark.text : Colors.light.text,
      paddingVertical: 0, // Remove default padding
    };
  };

  const getLabelStyle = (): TextStyle => {
    return {
      fontSize: FontSizes.sm,
      fontWeight: '500',
      color: isDark ? Colors.dark.text : Colors.light.text,
      marginBottom: Spacing.xs,
    };
  };

  const getIconColor = () => {
    if (error) return Colors.functional.error;
    if (isFocused) return Colors.primary.purple;
    return isDark ? Colors.dark.textSecondary : Colors.light.textSecondary;
  };

  return (
    <View style={containerStyle}>
      {label && (
        <Text style={[getLabelStyle(), labelStyle]}>
          {label}
          {required && <Text style={{ color: Colors.functional.error }}> *</Text>}
        </Text>
      )}
      
      <View style={getContainerStyle()}>
        {leftIcon && (
          <Ionicons
            name={leftIcon}
            size={20}
            color={getIconColor()}
            style={{ marginRight: Spacing.sm }}
          />
        )}
        
        <TextInput
          {...textInputProps}
          style={[getInputStyle(), inputStyle]}
          placeholderTextColor={isDark ? Colors.dark.textSecondary : Colors.light.textSecondary}
          onFocus={(e) => {
            setIsFocused(true);
            textInputProps.onFocus?.(e);
          }}
          onBlur={(e) => {
            setIsFocused(false);
            textInputProps.onBlur?.(e);
          }}
        />
        
        {rightIcon && (
          <Ionicons
            name={rightIcon}
            size={20}
            color={getIconColor()}
            style={{ marginLeft: Spacing.sm }}
            onPress={onRightIconPress}
          />
        )}
      </View>
      
      {error && (
        <Text style={styles.errorText}>
          {error}
        </Text>
      )}
      
      {hint && !error && (
        <Text style={[styles.hintText, { color: isDark ? Colors.dark.textSecondary : Colors.light.textSecondary }]}>
          {hint}
        </Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  errorText: {
    fontSize: FontSizes.xs,
    color: Colors.functional.error,
    marginTop: Spacing.xs,
  },
  hintText: {
    fontSize: FontSizes.xs,
    marginTop: Spacing.xs,
  },
});

// 预设输入框样式
export const OutlinedInput = (props: Omit<InputProps, 'variant'>) => (
  <Input {...props} variant="outlined" />
);

export const FilledInput = (props: Omit<InputProps, 'variant'>) => (
  <Input {...props} variant="filled" />
);

// 特殊用途输入框
export const SearchInput = (props: Omit<InputProps, 'leftIcon' | 'placeholder'>) => (
  <Input
    {...props}
    leftIcon="search"
    placeholder="搜索..."
    variant="filled"
  />
);

export const PasswordInput = (props: Omit<InputProps, 'secureTextEntry' | 'rightIcon'>) => {
  const [showPassword, setShowPassword] = useState(false);
  
  return (
    <Input
      {...props}
      secureTextEntry={!showPassword}
      rightIcon={showPassword ? "eye-off" : "eye"}
      onRightIconPress={() => setShowPassword(!showPassword)}
    />
  );
};
