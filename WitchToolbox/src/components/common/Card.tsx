import React from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Colors, Shadows } from '../../constants/colors';
import { Spacing, BorderRadius } from '../../constants/dimensions';

interface CardProps {
  children: React.ReactNode;
  onPress?: () => void;
  variant?: 'default' | 'elevated' | 'outlined' | 'gradient';
  padding?: number;
  margin?: number;
  style?: ViewStyle;
  gradient?: string[];
  theme?: 'light' | 'dark';
  shadow?: boolean;
  borderRadius?: number;
}

export default function Card({
  children,
  onPress,
  variant = 'default',
  padding = Spacing.md,
  margin = 0,
  style,
  gradient,
  theme = 'light',
  shadow = true,
  borderRadius = BorderRadius.card,
}: CardProps) {
  const isDark = theme === 'dark';

  const getCardStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius,
      padding,
      margin,
    };

    if (shadow && variant !== 'gradient') {
      Object.assign(baseStyle, Shadows.medium);
    }

    switch (variant) {
      case 'default':
        return {
          ...baseStyle,
          backgroundColor: isDark ? Colors.dark.card : Colors.light.card,
        };
      case 'elevated':
        return {
          ...baseStyle,
          backgroundColor: isDark ? Colors.dark.card : Colors.light.card,
          ...Shadows.large,
        };
      case 'outlined':
        return {
          ...baseStyle,
          backgroundColor: isDark ? Colors.dark.card : Colors.light.card,
          borderWidth: 1,
          borderColor: isDark ? Colors.dark.border : Colors.light.border,
        };
      case 'gradient':
        return {
          ...baseStyle,
          overflow: 'hidden',
        };
      default:
        return baseStyle;
    }
  };

  const renderCard = () => {
    if (variant === 'gradient') {
      const gradientColors = gradient || Colors.gradients.primary;
      
      return (
        <LinearGradient
          colors={gradientColors}
          style={[getCardStyle(), style]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          {children}
        </LinearGradient>
      );
    }

    return (
      <View style={[getCardStyle(), style]}>
        {children}
      </View>
    );
  };

  if (onPress) {
    return (
      <TouchableOpacity
        onPress={onPress}
        activeOpacity={0.8}
        style={{ borderRadius }}
      >
        {renderCard()}
      </TouchableOpacity>
    );
  }

  return renderCard();
}

// 预设卡片样式
export const DefaultCard = (props: Omit<CardProps, 'variant'>) => (
  <Card {...props} variant="default" />
);

export const ElevatedCard = (props: Omit<CardProps, 'variant'>) => (
  <Card {...props} variant="elevated" />
);

export const OutlinedCard = (props: Omit<CardProps, 'variant'>) => (
  <Card {...props} variant="outlined" />
);

export const GradientCard = (props: Omit<CardProps, 'variant'>) => (
  <Card {...props} variant="gradient" />
);

// 特殊用途卡片
export const MysticalCard = (props: Omit<CardProps, 'variant' | 'gradient'>) => (
  <Card {...props} variant="gradient" gradient={Colors.gradients.mystical} />
);

export const GoldenCard = (props: Omit<CardProps, 'variant' | 'gradient'>) => (
  <Card {...props} variant="gradient" gradient={Colors.gradients.golden} />
);

export const CosmicCard = (props: Omit<CardProps, 'variant' | 'gradient'>) => (
  <Card {...props} variant="gradient" gradient={Colors.gradients.cosmic} />
);

export const AuroraCard = (props: Omit<CardProps, 'variant' | 'gradient'>) => (
  <Card {...props} variant="gradient" gradient={Colors.gradients.aurora} />
);

export const SunsetCard = (props: Omit<CardProps, 'variant' | 'gradient'>) => (
  <Card {...props} variant="gradient" gradient={Colors.gradients.sunset} />
);
