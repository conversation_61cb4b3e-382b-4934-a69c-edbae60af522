import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  Animated,
  Easing,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../../constants/colors';
import { Spacing, FontSizes } from '../../constants/dimensions';

interface LoadingProps {
  visible?: boolean;
  text?: string;
  variant?: 'spinner' | 'dots' | 'mystical' | 'pulse';
  size?: 'small' | 'medium' | 'large';
  theme?: 'light' | 'dark';
  overlay?: boolean;
}

export default function Loading({
  visible = true,
  text,
  variant = 'mystical',
  size = 'medium',
  theme = 'light',
  overlay = false,
}: LoadingProps) {
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(0.3)).current;
  const isDark = theme === 'dark';

  useEffect(() => {
    if (visible) {
      startAnimations();
    }
  }, [visible]);

  const startAnimations = () => {
    // 旋转动画
    Animated.loop(
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 2000,
        easing: Easing.linear,
        useNativeDriver: true,
      })
    ).start();

    // 脉冲动画
    Animated.loop(
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 1.2,
          duration: 1000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 1000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
      ])
    ).start();

    // 透明度动画
    Animated.loop(
      Animated.sequence([
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 1500,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0.3,
          duration: 1500,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const getSize = () => {
    switch (size) {
      case 'small':
        return 24;
      case 'large':
        return 48;
      default:
        return 32;
    }
  };

  const renderSpinner = () => (
    <ActivityIndicator
      size={size === 'small' ? 'small' : 'large'}
      color={Colors.primary.purple}
    />
  );

  const renderDots = () => {
    const dotSize = size === 'small' ? 6 : size === 'large' ? 12 : 8;
    
    return (
      <View style={styles.dotsContainer}>
        {[0, 1, 2].map((index) => (
          <Animated.View
            key={index}
            style={[
              styles.dot,
              {
                width: dotSize,
                height: dotSize,
                backgroundColor: Colors.primary.purple,
                opacity: opacityAnim,
                transform: [
                  {
                    scale: scaleAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [1, 1.5],
                    }),
                  },
                ],
              },
            ]}
          />
        ))}
      </View>
    );
  };

  const renderMystical = () => {
    const iconSize = getSize();
    const rotate = rotateAnim.interpolate({
      inputRange: [0, 1],
      outputRange: ['0deg', '360deg'],
    });

    return (
      <View style={styles.mysticalContainer}>
        <Animated.View
          style={[
            styles.mysticalOuter,
            {
              transform: [{ rotate }],
              opacity: opacityAnim,
            },
          ]}
        >
          <LinearGradient
            colors={Colors.gradients.mystical}
            style={[styles.mysticalGradient, { width: iconSize * 2, height: iconSize * 2 }]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <Ionicons
              name="sparkles"
              size={iconSize}
              color={Colors.neutral.white}
            />
          </LinearGradient>
        </Animated.View>
        
        <Animated.View
          style={[
            styles.mysticalInner,
            {
              transform: [{ rotate: rotate, scale: scaleAnim }],
            },
          ]}
        >
          <Ionicons
            name="star"
            size={iconSize * 0.6}
            color={Colors.primary.gold}
          />
        </Animated.View>
      </View>
    );
  };

  const renderPulse = () => {
    const iconSize = getSize();
    
    return (
      <Animated.View
        style={[
          styles.pulseContainer,
          {
            transform: [{ scale: scaleAnim }],
            opacity: opacityAnim,
          },
        ]}
      >
        <LinearGradient
          colors={Colors.gradients.cosmic}
          style={[styles.pulseGradient, { width: iconSize * 1.5, height: iconSize * 1.5 }]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <Ionicons
            name="planet"
            size={iconSize}
            color={Colors.neutral.white}
          />
        </LinearGradient>
      </Animated.View>
    );
  };

  const renderLoadingIndicator = () => {
    switch (variant) {
      case 'spinner':
        return renderSpinner();
      case 'dots':
        return renderDots();
      case 'pulse':
        return renderPulse();
      default:
        return renderMystical();
    }
  };

  if (!visible) return null;

  const containerStyle = overlay
    ? [
        styles.overlay,
        { backgroundColor: isDark ? Colors.dark.background + '80' : Colors.light.background + '80' },
      ]
    : styles.container;

  return (
    <View style={containerStyle}>
      <View style={styles.content}>
        {renderLoadingIndicator()}
        {text && (
          <Text
            style={[
              styles.text,
              {
                color: isDark ? Colors.dark.text : Colors.light.text,
                fontSize: size === 'small' ? FontSizes.sm : FontSizes.md,
              },
            ]}
          >
            {text}
          </Text>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000,
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    marginTop: Spacing.md,
    fontWeight: '500',
    textAlign: 'center',
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dot: {
    borderRadius: 50,
    marginHorizontal: 4,
  },
  mysticalContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  mysticalOuter: {
    position: 'absolute',
  },
  mysticalGradient: {
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  mysticalInner: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  pulseContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  pulseGradient: {
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

// 预设加载样式
export const SpinnerLoading = (props: Omit<LoadingProps, 'variant'>) => (
  <Loading {...props} variant="spinner" />
);

export const DotsLoading = (props: Omit<LoadingProps, 'variant'>) => (
  <Loading {...props} variant="dots" />
);

export const MysticalLoading = (props: Omit<LoadingProps, 'variant'>) => (
  <Loading {...props} variant="mystical" />
);

export const PulseLoading = (props: Omit<LoadingProps, 'variant'>) => (
  <Loading {...props} variant="pulse" />
);
