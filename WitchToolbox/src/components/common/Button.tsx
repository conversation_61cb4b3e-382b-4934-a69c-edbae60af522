import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../../constants/colors';
import { Spacing, FontSizes, BorderRadius, ButtonSizes } from '../../constants/dimensions';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'gradient';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  icon?: keyof typeof Ionicons.glyphMap;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  gradient?: string[];
  theme?: 'light' | 'dark';
}

export default function Button({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  style,
  textStyle,
  gradient,
  theme = 'light',
}: ButtonProps) {
  const isDark = theme === 'dark';
  const buttonSize = ButtonSizes[size];
  
  const getButtonStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      height: buttonSize.height,
      paddingHorizontal: buttonSize.paddingHorizontal,
      borderRadius: BorderRadius.button,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      opacity: disabled ? 0.6 : 1,
    };

    if (fullWidth) {
      baseStyle.width = '100%';
    }

    switch (variant) {
      case 'primary':
        return {
          ...baseStyle,
          backgroundColor: Colors.primary.purple,
        };
      case 'secondary':
        return {
          ...baseStyle,
          backgroundColor: isDark ? Colors.dark.surface : Colors.light.surface,
          borderWidth: 1,
          borderColor: isDark ? Colors.dark.border : Colors.light.border,
        };
      case 'outline':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
          borderWidth: 2,
          borderColor: Colors.primary.purple,
        };
      case 'ghost':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
        };
      case 'gradient':
        return baseStyle;
      default:
        return baseStyle;
    }
  };

  const getTextStyle = (): TextStyle => {
    const baseStyle: TextStyle = {
      fontSize: buttonSize.fontSize,
      fontWeight: '600',
    };

    switch (variant) {
      case 'primary':
        return {
          ...baseStyle,
          color: Colors.neutral.white,
        };
      case 'secondary':
        return {
          ...baseStyle,
          color: isDark ? Colors.dark.text : Colors.light.text,
        };
      case 'outline':
        return {
          ...baseStyle,
          color: Colors.primary.purple,
        };
      case 'ghost':
        return {
          ...baseStyle,
          color: Colors.primary.purple,
        };
      case 'gradient':
        return {
          ...baseStyle,
          color: Colors.neutral.white,
        };
      default:
        return baseStyle;
    }
  };

  const renderContent = () => {
    if (loading) {
      return (
        <ActivityIndicator
          size="small"
          color={variant === 'primary' || variant === 'gradient' ? Colors.neutral.white : Colors.primary.purple}
        />
      );
    }

    const iconColor = variant === 'primary' || variant === 'gradient' 
      ? Colors.neutral.white 
      : Colors.primary.purple;

    return (
      <>
        {icon && iconPosition === 'left' && (
          <Ionicons
            name={icon}
            size={20}
            color={iconColor}
            style={{ marginRight: Spacing.xs }}
          />
        )}
        <Text style={[getTextStyle(), textStyle]}>
          {title}
        </Text>
        {icon && iconPosition === 'right' && (
          <Ionicons
            name={icon}
            size={20}
            color={iconColor}
            style={{ marginLeft: Spacing.xs }}
          />
        )}
      </>
    );
  };

  if (variant === 'gradient') {
    const gradientColors = gradient || Colors.gradients.primary;
    
    return (
      <TouchableOpacity
        onPress={onPress}
        disabled={disabled || loading}
        activeOpacity={0.8}
        style={style}
      >
        <LinearGradient
          colors={gradientColors}
          style={[getButtonStyle(), style]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          {renderContent()}
        </LinearGradient>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      style={[getButtonStyle(), style]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
    >
      {renderContent()}
    </TouchableOpacity>
  );
}

// 预设按钮样式
export const PrimaryButton = (props: Omit<ButtonProps, 'variant'>) => (
  <Button {...props} variant="primary" />
);

export const SecondaryButton = (props: Omit<ButtonProps, 'variant'>) => (
  <Button {...props} variant="secondary" />
);

export const OutlineButton = (props: Omit<ButtonProps, 'variant'>) => (
  <Button {...props} variant="outline" />
);

export const GhostButton = (props: Omit<ButtonProps, 'variant'>) => (
  <Button {...props} variant="ghost" />
);

export const GradientButton = (props: Omit<ButtonProps, 'variant'>) => (
  <Button {...props} variant="gradient" />
);

// 特殊用途按钮
export const MysticalButton = (props: Omit<ButtonProps, 'variant' | 'gradient'>) => (
  <Button {...props} variant="gradient" gradient={Colors.gradients.mystical} />
);

export const GoldenButton = (props: Omit<ButtonProps, 'variant' | 'gradient'>) => (
  <Button {...props} variant="gradient" gradient={Colors.gradients.golden} />
);

export const CosmicButton = (props: Omit<ButtonProps, 'variant' | 'gradient'>) => (
  <Button {...props} variant="gradient" gradient={Colors.gradients.cosmic} />
);
