// 颜色系统 - 类iOS风格的神秘学主题

export const Colors = {
  // 主色调
  primary: {
    purple: '#6B46C1',      // 深紫色 - 神秘、智慧
    gold: '#F59E0B',        // 金色 - 高贵、能量
    blue: '#1E40AF',        // 神秘蓝 - 深邃、宁静
  },

  // 辅助色彩
  secondary: {
    roseGold: '#E879F9',    // 玫瑰金 - 女性化、温暖
    emerald: '#10B981',     // 翡翠绿 - 自然、疗愈
    pearl: '#F8FAFC',       // 珍珠白 - 纯净、平衡
    cosmic: '#4C1D95',      // 宇宙紫 - 深邃、神秘
    moonlight: '#E5E7EB',   // 月光银 - 宁静、优雅
  },

  // 渐变色
  gradients: {
    primary: ['#6B46C1', '#9333EA'],           // 紫色渐变
    mystical: ['#1E40AF', '#6B46C1', '#9333EA'], // 神秘渐变
    golden: ['#F59E0B', '#FBBF24'],            // 金色渐变
    cosmic: ['#1E1B4B', '#4C1D95', '#6B46C1'], // 宇宙渐变
    aurora: ['#10B981', '#3B82F6', '#8B5CF6'], // 极光渐变
    sunset: ['#F59E0B', '#EF4444', '#EC4899'], // 日落渐变
  },

  // 功能色彩
  functional: {
    success: '#10B981',     // 成功绿
    warning: '#F59E0B',     // 警告橙
    error: '#EF4444',       // 错误红
    info: '#3B82F6',        // 信息蓝
  },

  // 中性色
  neutral: {
    white: '#FFFFFF',
    gray50: '#F9FAFB',
    gray100: '#F3F4F6',
    gray200: '#E5E7EB',
    gray300: '#D1D5DB',
    gray400: '#9CA3AF',
    gray500: '#6B7280',
    gray600: '#4B5563',
    gray700: '#374151',
    gray800: '#1F2937',
    gray900: '#111827',
    black: '#000000',
  },

  // 暗色主题
  dark: {
    background: '#0F0F23',   // 深空背景
    surface: '#1A1A2E',     // 表面色
    card: '#16213E',        // 卡片背景
    border: '#2D3748',      // 边框色
    text: '#F7FAFC',        // 主文本
    textSecondary: '#A0AEC0', // 次要文本
    accent: '#6B46C1',      // 强调色
  },

  // 亮色主题
  light: {
    background: '#FFFFFF',   // 纯白背景
    surface: '#F8FAFC',     // 表面色
    card: '#FFFFFF',        // 卡片背景
    border: '#E2E8F0',      // 边框色
    text: '#1A202C',        // 主文本
    textSecondary: '#4A5568', // 次要文本
    accent: '#6B46C1',      // 强调色
  },

  // 卡牌相关色彩
  tarot: {
    major: '#6B46C1',       // 大阿卡纳
    wands: '#EF4444',       // 权杖 - 火元素
    cups: '#3B82F6',        // 圣杯 - 水元素
    swords: '#F59E0B',      // 宝剑 - 风元素
    pentacles: '#10B981',   // 星币 - 土元素
  },

  // 脉轮色彩
  chakras: {
    root: '#DC2626',        // 海底轮 - 红色
    sacral: '#EA580C',      // 脐轮 - 橙色
    solarPlexus: '#FBBF24', // 太阳轮 - 黄色
    heart: '#10B981',       // 心轮 - 绿色
    throat: '#3B82F6',      // 喉轮 - 蓝色
    thirdEye: '#6366F1',    // 眉心轮 - 靛色
    crown: '#8B5CF6',       // 顶轮 - 紫色
  },

  // 星座色彩
  zodiac: {
    fire: '#EF4444',        // 火象星座
    earth: '#10B981',       // 土象星座
    air: '#F59E0B',         // 风象星座
    water: '#3B82F6',       // 水象星座
  },

  // 月相色彩
  moonPhases: {
    new: '#1F2937',         // 新月
    waxing: '#6B7280',      // 上弦月
    full: '#F9FAFB',        // 满月
    waning: '#9CA3AF',      // 下弦月
  },

  // 透明度变体
  opacity: {
    10: '1A',  // 10%
    20: '33',  // 20%
    30: '4D',  // 30%
    40: '66',  // 40%
    50: '80',  // 50%
    60: '99',  // 60%
    70: 'B3',  // 70%
    80: 'CC',  // 80%
    90: 'E6',  // 90%
  },
};

// 主题配置
export const Theme = {
  light: {
    colors: {
      primary: Colors.primary.purple,
      background: Colors.light.background,
      card: Colors.light.card,
      text: Colors.light.text,
      border: Colors.light.border,
      notification: Colors.functional.info,
    },
  },
  dark: {
    colors: {
      primary: Colors.primary.purple,
      background: Colors.dark.background,
      card: Colors.dark.card,
      text: Colors.dark.text,
      border: Colors.dark.border,
      notification: Colors.functional.info,
    },
  },
};

// 阴影样式
export const Shadows = {
  small: {
    shadowColor: Colors.neutral.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  medium: {
    shadowColor: Colors.neutral.black,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 6.27,
    elevation: 10,
  },
  large: {
    shadowColor: Colors.neutral.black,
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.2,
    shadowRadius: 10.32,
    elevation: 16,
  },
  glow: {
    shadowColor: Colors.primary.purple,
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    elevation: 8,
  },
};

export default Colors;
