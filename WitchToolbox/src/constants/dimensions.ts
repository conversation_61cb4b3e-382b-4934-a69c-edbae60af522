import { Dimensions, Platform } from 'react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// 屏幕尺寸
export const Screen = {
  width: screenWidth,
  height: screenHeight,
  isSmall: screenWidth < 375,
  isMedium: screenWidth >= 375 && screenWidth < 414,
  isLarge: screenWidth >= 414,
  isIOS: Platform.OS === 'ios',
  isAndroid: Platform.OS === 'android',
};

// 间距系统 - 基于8px网格
export const Spacing = {
  xs: 4,    // 极小间距
  sm: 8,    // 小间距
  md: 16,   // 中等间距
  lg: 24,   // 大间距
  xl: 32,   // 超大间距
  xxl: 48,  // 极大间距
  
  // 特殊间距
  cardPadding: 16,
  screenPadding: 20,
  sectionSpacing: 24,
  componentSpacing: 12,
};

// 字体大小系统
export const FontSizes = {
  xs: 12,   // 极小文字
  sm: 14,   // 小文字
  md: 16,   // 中等文字（默认）
  lg: 18,   // 大文字
  xl: 20,   // 超大文字
  xxl: 24,  // 极大文字
  
  // 标题字体
  h1: 32,   // 主标题
  h2: 28,   // 二级标题
  h3: 24,   // 三级标题
  h4: 20,   // 四级标题
  h5: 18,   // 五级标题
  h6: 16,   // 六级标题
  
  // 特殊字体
  caption: 12,    // 说明文字
  button: 16,     // 按钮文字
  tabBar: 10,     // 标签栏文字
  badge: 12,      // 徽章文字
};

// 字体权重
export const FontWeights = {
  light: '300',
  regular: '400',
  medium: '500',
  semibold: '600',
  bold: '700',
  heavy: '800',
};

// 行高
export const LineHeights = {
  tight: 1.2,
  normal: 1.4,
  relaxed: 1.6,
  loose: 1.8,
};

// 圆角半径
export const BorderRadius = {
  xs: 4,    // 极小圆角
  sm: 8,    // 小圆角
  md: 12,   // 中等圆角
  lg: 16,   // 大圆角
  xl: 20,   // 超大圆角
  xxl: 24,  // 极大圆角
  full: 9999, // 完全圆角
  
  // 特殊圆角
  card: 16,
  button: 12,
  input: 8,
  modal: 20,
};

// 边框宽度
export const BorderWidths = {
  thin: 0.5,
  normal: 1,
  thick: 2,
  heavy: 3,
};

// 图标尺寸
export const IconSizes = {
  xs: 12,
  sm: 16,
  md: 20,
  lg: 24,
  xl: 28,
  xxl: 32,
  
  // 特殊图标尺寸
  tabBar: 24,
  button: 20,
  avatar: 40,
  cardIcon: 32,
};

// 按钮尺寸
export const ButtonSizes = {
  small: {
    height: 32,
    paddingHorizontal: 16,
    fontSize: FontSizes.sm,
  },
  medium: {
    height: 44,
    paddingHorizontal: 20,
    fontSize: FontSizes.md,
  },
  large: {
    height: 52,
    paddingHorizontal: 24,
    fontSize: FontSizes.lg,
  },
};

// 输入框尺寸
export const InputSizes = {
  small: {
    height: 36,
    paddingHorizontal: 12,
    fontSize: FontSizes.sm,
  },
  medium: {
    height: 44,
    paddingHorizontal: 16,
    fontSize: FontSizes.md,
  },
  large: {
    height: 52,
    paddingHorizontal: 20,
    fontSize: FontSizes.lg,
  },
};

// 卡片尺寸
export const CardSizes = {
  small: {
    width: screenWidth * 0.4,
    height: screenWidth * 0.6,
  },
  medium: {
    width: screenWidth * 0.6,
    height: screenWidth * 0.9,
  },
  large: {
    width: screenWidth * 0.8,
    height: screenWidth * 1.2,
  },
  
  // 塔罗牌标准比例 (约2.75:4.75)
  tarot: {
    width: screenWidth * 0.25,
    height: screenWidth * 0.43,
  },
  
  // 神谕卡比例 (约3:4)
  oracle: {
    width: screenWidth * 0.3,
    height: screenWidth * 0.4,
  },
};

// 动画持续时间
export const AnimationDurations = {
  fast: 150,
  normal: 250,
  slow: 350,
  verySlow: 500,
  
  // 特殊动画
  cardFlip: 600,
  pageTransition: 300,
  modalPresent: 250,
  tabSwitch: 200,
};

// 动画缓动函数
export const AnimationEasing = {
  easeInOut: 'ease-in-out',
  easeIn: 'ease-in',
  easeOut: 'ease-out',
  linear: 'linear',
  
  // 自定义缓动
  spring: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  smooth: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
};

// Z-index层级
export const ZIndex = {
  background: -1,
  normal: 0,
  elevated: 10,
  overlay: 100,
  modal: 1000,
  popover: 1100,
  tooltip: 1200,
  notification: 1300,
  maximum: 9999,
};

// 透明度级别
export const Opacity = {
  disabled: 0.3,
  muted: 0.6,
  normal: 1.0,
  
  // 背景透明度
  backdrop: 0.5,
  overlay: 0.8,
  subtle: 0.1,
};

// 安全区域
export const SafeArea = {
  top: Platform.OS === 'ios' ? 44 : 0,
  bottom: Platform.OS === 'ios' ? 34 : 0,
};

// 导航栏高度
export const NavigationBar = {
  height: Platform.OS === 'ios' ? 44 : 56,
  tabBarHeight: Platform.OS === 'ios' ? 83 : 60,
};

// 响应式断点
export const Breakpoints = {
  small: 0,
  medium: 768,
  large: 1024,
  xlarge: 1280,
};

// 网格系统
export const Grid = {
  columns: 12,
  gutter: 16,
  margin: 20,
};

export default {
  Screen,
  Spacing,
  FontSizes,
  FontWeights,
  LineHeights,
  BorderRadius,
  BorderWidths,
  IconSizes,
  ButtonSizes,
  InputSizes,
  CardSizes,
  AnimationDurations,
  AnimationEasing,
  ZIndex,
  Opacity,
  SafeArea,
  NavigationBar,
  Breakpoints,
  Grid,
};
