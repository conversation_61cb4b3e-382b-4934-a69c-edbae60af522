import { TarotCard, OracleCard } from '../types';
import { majorArcana } from '../data/tarot/majorArcana';
import { wandsSuit } from '../data/tarot/minorArcana';
import { angelCards } from '../data/oracle/angelCards';

// 获取所有塔罗牌
export const getAllTarotCards = (): TarotCard[] => {
  return [...majorArcana, ...wandsSuit];
};

// 获取大阿卡纳
export const getMajorArcana = (): TarotCard[] => {
  return majorArcana;
};

// 获取小阿卡纳
export const getMinorArcana = (): TarotCard[] => {
  return [wandsSuit].flat();
};

// 根据花色获取小阿卡纳
export const getMinorArcanaBySuit = (suit: 'wands' | 'cups' | 'swords' | 'pentacles'): TarotCard[] => {
  switch (suit) {
    case 'wands':
      return wandsSuit;
    default:
      return [];
  }
};

// 获取神谕卡
export const getOracleCards = (deck?: string): OracleCard[] => {
  if (deck === 'angel-cards') {
    return angelCards;
  }
  return angelCards; // 默认返回天使卡
};

// 洗牌算法 - Fisher-Yates shuffle
export const shuffleCards = <T>(cards: T[]): T[] => {
  const shuffled = [...cards];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

// 从牌组中抽取指定数量的牌
export const drawCards = <T>(cards: T[], count: number): T[] => {
  const shuffled = shuffleCards(cards);
  return shuffled.slice(0, count);
};

// 根据ID获取塔罗牌
export const getTarotCardById = (id: string): TarotCard | undefined => {
  const allCards = getAllTarotCards();
  return allCards.find(card => card.id === id);
};

// 根据ID获取神谕卡
export const getOracleCardById = (id: string): OracleCard | undefined => {
  const allCards = getOracleCards();
  return allCards.find(card => card.id === id);
};

// 获取随机塔罗牌
export const getRandomTarotCard = (): TarotCard => {
  const allCards = getAllTarotCards();
  const randomIndex = Math.floor(Math.random() * allCards.length);
  return allCards[randomIndex];
};

// 获取随机神谕卡
export const getRandomOracleCard = (deck?: string): OracleCard => {
  const cards = getOracleCards(deck);
  const randomIndex = Math.floor(Math.random() * cards.length);
  return cards[randomIndex];
};

// 塔罗牌阵配置
export interface TarotSpread {
  id: string;
  name: string;
  description: string;
  cardCount: number;
  positions: SpreadPosition[];
}

export interface SpreadPosition {
  id: string;
  name: string;
  description: string;
  x: number; // 相对位置 (0-1)
  y: number; // 相对位置 (0-1)
  rotation?: number; // 旋转角度
}

// 预定义的塔罗牌阵
export const tarotSpreads: TarotSpread[] = [
  {
    id: 'single-card',
    name: '单张抽牌',
    description: '最简单的占卜方式，适合日常指引',
    cardCount: 1,
    positions: [
      {
        id: 'card1',
        name: '指引',
        description: '今日的指引和建议',
        x: 0.5,
        y: 0.5,
      },
    ],
  },
  {
    id: 'three-card',
    name: '三张牌阵',
    description: '过去、现在、未来的时间线占卜',
    cardCount: 3,
    positions: [
      {
        id: 'past',
        name: '过去',
        description: '影响当前情况的过去因素',
        x: 0.2,
        y: 0.5,
      },
      {
        id: 'present',
        name: '现在',
        description: '当前的状况和挑战',
        x: 0.5,
        y: 0.5,
      },
      {
        id: 'future',
        name: '未来',
        description: '可能的发展方向',
        x: 0.8,
        y: 0.5,
      },
    ],
  },
  {
    id: 'love-triangle',
    name: '爱情三角',
    description: '专门用于感情问题的占卜',
    cardCount: 3,
    positions: [
      {
        id: 'you',
        name: '你的状态',
        description: '你在这段关系中的状态和感受',
        x: 0.5,
        y: 0.2,
      },
      {
        id: 'partner',
        name: '对方状态',
        description: '对方在这段关系中的状态和感受',
        x: 0.2,
        y: 0.8,
      },
      {
        id: 'relationship',
        name: '关系发展',
        description: '这段关系的发展方向和建议',
        x: 0.8,
        y: 0.8,
      },
    ],
  },
  {
    id: 'career-cross',
    name: '事业十字',
    description: '专门用于事业和工作问题的占卜',
    cardCount: 5,
    positions: [
      {
        id: 'current',
        name: '当前状况',
        description: '你目前的工作状况',
        x: 0.5,
        y: 0.5,
      },
      {
        id: 'challenge',
        name: '面临挑战',
        description: '工作中面临的主要挑战',
        x: 0.5,
        y: 0.2,
      },
      {
        id: 'past',
        name: '过去影响',
        description: '过去经历对现在的影响',
        x: 0.2,
        y: 0.5,
      },
      {
        id: 'future',
        name: '未来发展',
        description: '事业的发展方向',
        x: 0.8,
        y: 0.5,
      },
      {
        id: 'advice',
        name: '建议指导',
        description: '对事业发展的建议',
        x: 0.5,
        y: 0.8,
      },
    ],
  },
];

// 根据ID获取牌阵
export const getSpreadById = (id: string): TarotSpread | undefined => {
  return tarotSpreads.find(spread => spread.id === id);
};

// 获取所有可用的牌阵
export const getAllSpreads = (): TarotSpread[] => {
  return tarotSpreads;
};

// 为指定牌阵抽牌
export const drawCardsForSpread = (spreadId: string, cards?: TarotCard[]): TarotCard[] => {
  const spread = getSpreadById(spreadId);
  if (!spread) return [];
  
  const cardDeck = cards || getAllTarotCards();
  return drawCards(cardDeck, spread.cardCount);
};

// 生成占卜解读
export interface ReadingInterpretation {
  spreadId: string;
  cards: TarotCard[];
  positions: SpreadPosition[];
  interpretation: string;
  summary: string;
}

export const generateReading = (
  spreadId: string,
  cards: TarotCard[],
  isReversed?: boolean[]
): ReadingInterpretation => {
  const spread = getSpreadById(spreadId);
  if (!spread) {
    throw new Error('Invalid spread ID');
  }

  let interpretation = '';
  let summary = '';

  // 根据不同牌阵生成解读
  switch (spreadId) {
    case 'single-card':
      const card = cards[0];
      const meanings = isReversed?.[0] ? card.meanings.reversed : card.meanings.upright;
      interpretation = `${card.name}代表${meanings.join('、')}。${card.description}`;
      summary = `今日的指引是${card.name}，提醒你要${meanings[0]}。`;
      break;
      
    case 'three-card':
      interpretation = cards.map((card, index) => {
        const position = spread.positions[index];
        const meanings = isReversed?.[index] ? card.meanings.reversed : card.meanings.upright;
        return `${position.name}：${card.name} - ${meanings.slice(0, 2).join('、')}`;
      }).join('\n\n');
      summary = `从过去的${cards[0].name}到现在的${cards[1].name}，未来将迎来${cards[2].name}的能量。`;
      break;
      
    default:
      interpretation = cards.map((card, index) => {
        const position = spread.positions[index];
        const meanings = isReversed?.[index] ? card.meanings.reversed : card.meanings.upright;
        return `${position.name}：${card.name} - ${meanings[0]}`;
      }).join('\n\n');
      summary = `这次占卜显示了${spread.name}的完整画面，需要综合考虑各个方面的影响。`;
  }

  return {
    spreadId,
    cards,
    positions: spread.positions,
    interpretation,
    summary,
  };
};
