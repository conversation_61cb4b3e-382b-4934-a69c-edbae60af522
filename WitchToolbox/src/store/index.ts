import { configureStore } from '@reduxjs/toolkit';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';

// Import slices
import userSlice from './slices/userSlice';
import divinationSlice from './slices/divinationSlice';
import learningSlice from './slices/learningSlice';
import appSlice from './slices/appSlice';

// Configure store
export const store = configureStore({
  reducer: {
    user: userSlice,
    divination: divinationSlice,
    learning: learningSlice,
    app: appSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

// Export types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Export typed hooks
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

export default store;
