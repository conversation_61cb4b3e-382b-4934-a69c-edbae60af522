import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { DivinationReading, TarotCard, OracleCard } from '../../types';

interface DivinationState {
  currentReading: DivinationReading | null;
  history: DivinationReading[];
  selectedCards: (TarotCard | OracleCard)[];
  isReading: boolean;
  currentSpread: string | null;
  shuffledDeck: (TarotCard | OracleCard)[];
}

const initialState: DivinationState = {
  currentReading: null,
  history: [],
  selectedCards: [],
  isReading: false,
  currentSpread: null,
  shuffledDeck: [],
};

const divinationSlice = createSlice({
  name: 'divination',
  initialState,
  reducers: {
    startReading: (state, action: PayloadAction<{ type: string; spread: string }>) => {
      state.isReading = true;
      state.currentSpread = action.payload.spread;
      state.selectedCards = [];
      state.currentReading = {
        id: Date.now().toString(),
        type: action.payload.type as any,
        date: new Date().toISOString(),
        question: '',
        cards: [],
        interpretation: '',
        spread: action.payload.spread,
        tags: [],
        isForOthers: false,
      };
    },
    
    endReading: (state) => {
      state.isReading = false;
      state.currentSpread = null;
      state.selectedCards = [];
      if (state.currentReading) {
        state.history.unshift(state.currentReading);
        state.currentReading = null;
      }
    },
    
    selectCard: (state, action: PayloadAction<TarotCard | OracleCard>) => {
      state.selectedCards.push(action.payload);
      if (state.currentReading) {
        state.currentReading.cards.push(action.payload);
      }
    },
    
    removeCard: (state, action: PayloadAction<number>) => {
      state.selectedCards.splice(action.payload, 1);
      if (state.currentReading) {
        state.currentReading.cards.splice(action.payload, 1);
      }
    },
    
    updateReading: (state, action: PayloadAction<Partial<DivinationReading>>) => {
      if (state.currentReading) {
        state.currentReading = { ...state.currentReading, ...action.payload };
      }
    },
    
    saveReading: (state, action: PayloadAction<DivinationReading>) => {
      const existingIndex = state.history.findIndex(r => r.id === action.payload.id);
      if (existingIndex >= 0) {
        state.history[existingIndex] = action.payload;
      } else {
        state.history.unshift(action.payload);
      }
    },
    
    deleteReading: (state, action: PayloadAction<string>) => {
      state.history = state.history.filter(r => r.id !== action.payload);
    },
    
    shuffleDeck: (state, action: PayloadAction<(TarotCard | OracleCard)[]>) => {
      // Fisher-Yates shuffle algorithm
      const deck = [...action.payload];
      for (let i = deck.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [deck[i], deck[j]] = [deck[j], deck[i]];
      }
      state.shuffledDeck = deck;
    },
    
    clearSelectedCards: (state) => {
      state.selectedCards = [];
    },
    
    setQuestion: (state, action: PayloadAction<string>) => {
      if (state.currentReading) {
        state.currentReading.question = action.payload;
      }
    },
    
    setInterpretation: (state, action: PayloadAction<string>) => {
      if (state.currentReading) {
        state.currentReading.interpretation = action.payload;
      }
    },
    
    addTag: (state, action: PayloadAction<string>) => {
      if (state.currentReading && !state.currentReading.tags.includes(action.payload)) {
        state.currentReading.tags.push(action.payload);
      }
    },
    
    removeTag: (state, action: PayloadAction<string>) => {
      if (state.currentReading) {
        state.currentReading.tags = state.currentReading.tags.filter(tag => tag !== action.payload);
      }
    },
    
    setClientName: (state, action: PayloadAction<string>) => {
      if (state.currentReading) {
        state.currentReading.clientName = action.payload;
        state.currentReading.isForOthers = action.payload.length > 0;
      }
    },
  },
});

export const {
  startReading,
  endReading,
  selectCard,
  removeCard,
  updateReading,
  saveReading,
  deleteReading,
  shuffleDeck,
  clearSelectedCards,
  setQuestion,
  setInterpretation,
  addTag,
  removeTag,
  setClientName,
} = divinationSlice.actions;

export default divinationSlice.reducer;
