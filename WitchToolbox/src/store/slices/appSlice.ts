import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Notification } from '../../types';

interface AppState {
  isLoading: boolean;
  theme: 'light' | 'dark';
  notifications: Notification[];
  isFirstLaunch: boolean;
  currentScreen: string;
  networkStatus: 'online' | 'offline';
  soundEnabled: boolean;
  hapticEnabled: boolean;
}

const initialState: AppState = {
  isLoading: false,
  theme: 'light',
  notifications: [],
  isFirstLaunch: true,
  currentScreen: 'Home',
  networkStatus: 'online',
  soundEnabled: true,
  hapticEnabled: true,
};

const appSlice = createSlice({
  name: 'app',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    
    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {
      state.theme = action.payload;
    },
    
    toggleTheme: (state) => {
      state.theme = state.theme === 'light' ? 'dark' : 'light';
    },
    
    addNotification: (state, action: PayloadAction<Omit<Notification, 'id' | 'timestamp' | 'read'>>) => {
      const notification: Notification = {
        ...action.payload,
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        read: false,
      };
      state.notifications.unshift(notification);
      
      // 限制通知数量，最多保留50条
      if (state.notifications.length > 50) {
        state.notifications = state.notifications.slice(0, 50);
      }
    },
    
    markNotificationAsRead: (state, action: PayloadAction<string>) => {
      const notification = state.notifications.find(n => n.id === action.payload);
      if (notification) {
        notification.read = true;
      }
    },
    
    markAllNotificationsAsRead: (state) => {
      state.notifications.forEach(notification => {
        notification.read = true;
      });
    },
    
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(n => n.id !== action.payload);
    },
    
    clearAllNotifications: (state) => {
      state.notifications = [];
    },
    
    setFirstLaunch: (state, action: PayloadAction<boolean>) => {
      state.isFirstLaunch = action.payload;
    },
    
    setCurrentScreen: (state, action: PayloadAction<string>) => {
      state.currentScreen = action.payload;
    },
    
    setNetworkStatus: (state, action: PayloadAction<'online' | 'offline'>) => {
      state.networkStatus = action.payload;
    },
    
    setSoundEnabled: (state, action: PayloadAction<boolean>) => {
      state.soundEnabled = action.payload;
    },
    
    setHapticEnabled: (state, action: PayloadAction<boolean>) => {
      state.hapticEnabled = action.payload;
    },
    
    showSuccessMessage: (state, action: PayloadAction<string>) => {
      const notification: Notification = {
        id: Date.now().toString(),
        title: '成功',
        message: action.payload,
        type: 'success',
        timestamp: new Date().toISOString(),
        read: false,
      };
      state.notifications.unshift(notification);
    },
    
    showErrorMessage: (state, action: PayloadAction<string>) => {
      const notification: Notification = {
        id: Date.now().toString(),
        title: '错误',
        message: action.payload,
        type: 'error',
        timestamp: new Date().toISOString(),
        read: false,
      };
      state.notifications.unshift(notification);
    },
    
    showInfoMessage: (state, action: PayloadAction<string>) => {
      const notification: Notification = {
        id: Date.now().toString(),
        title: '提示',
        message: action.payload,
        type: 'info',
        timestamp: new Date().toISOString(),
        read: false,
      };
      state.notifications.unshift(notification);
    },
    
    showWarningMessage: (state, action: PayloadAction<string>) => {
      const notification: Notification = {
        id: Date.now().toString(),
        title: '警告',
        message: action.payload,
        type: 'warning',
        timestamp: new Date().toISOString(),
        read: false,
      };
      state.notifications.unshift(notification);
    },
  },
});

export const {
  setLoading,
  setTheme,
  toggleTheme,
  addNotification,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  removeNotification,
  clearAllNotifications,
  setFirstLaunch,
  setCurrentScreen,
  setNetworkStatus,
  setSoundEnabled,
  setHapticEnabled,
  showSuccessMessage,
  showErrorMessage,
  showInfoMessage,
  showWarningMessage,
} = appSlice.actions;

export default appSlice.reducer;
