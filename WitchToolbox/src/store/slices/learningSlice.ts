import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Course, LearningProgress, Achievement } from '../../types';

interface LearningState {
  courses: Course[];
  currentCourse: Course | null;
  progress: LearningProgress[];
  achievements: Achievement[];
  dailyStreak: number;
  totalStudyTime: number; // 总学习时间（分钟）
  lastStudyDate: string | null;
}

const initialState: LearningState = {
  courses: [],
  currentCourse: null,
  progress: [],
  achievements: [],
  dailyStreak: 0,
  totalStudyTime: 0,
  lastStudyDate: null,
};

const learningSlice = createSlice({
  name: 'learning',
  initialState,
  reducers: {
    setCourses: (state, action: PayloadAction<Course[]>) => {
      state.courses = action.payload;
    },
    
    setCurrentCourse: (state, action: PayloadAction<Course | null>) => {
      state.currentCourse = action.payload;
    },
    
    startCourse: (state, action: PayloadAction<string>) => {
      const course = state.courses.find(c => c.id === action.payload);
      if (course) {
        state.currentCourse = course;
        
        // 创建学习进度记录
        const existingProgress = state.progress.find(p => p.courseId === action.payload);
        if (!existingProgress) {
          const newProgress: LearningProgress = {
            userId: 'current-user', // 实际应用中应该从用户状态获取
            courseId: action.payload,
            completedLessons: [],
            currentLesson: course.lessons[0]?.id || '',
            progress: 0,
            startDate: new Date().toISOString(),
            lastAccessDate: new Date().toISOString(),
            timeSpent: 0,
          };
          state.progress.push(newProgress);
        }
      }
    },
    
    completeLesson: (state, action: PayloadAction<{ courseId: string; lessonId: string; timeSpent: number }>) => {
      const { courseId, lessonId, timeSpent } = action.payload;
      const progressIndex = state.progress.findIndex(p => p.courseId === courseId);
      
      if (progressIndex >= 0) {
        const progress = state.progress[progressIndex];
        
        // 添加已完成课程
        if (!progress.completedLessons.includes(lessonId)) {
          progress.completedLessons.push(lessonId);
        }
        
        // 更新学习时间
        progress.timeSpent += timeSpent;
        state.totalStudyTime += timeSpent;
        
        // 更新最后访问时间
        progress.lastAccessDate = new Date().toISOString();
        
        // 计算进度百分比
        const course = state.courses.find(c => c.id === courseId);
        if (course) {
          progress.progress = (progress.completedLessons.length / course.lessons.length) * 100;
          
          // 设置下一课程
          const currentLessonIndex = course.lessons.findIndex(l => l.id === lessonId);
          if (currentLessonIndex < course.lessons.length - 1) {
            progress.currentLesson = course.lessons[currentLessonIndex + 1].id;
          }
        }
        
        // 更新每日学习连续天数
        const today = new Date().toDateString();
        const lastStudy = state.lastStudyDate ? new Date(state.lastStudyDate).toDateString() : null;
        
        if (lastStudy !== today) {
          if (lastStudy === new Date(Date.now() - 24 * 60 * 60 * 1000).toDateString()) {
            // 连续学习
            state.dailyStreak += 1;
          } else if (lastStudy !== today) {
            // 重新开始计数
            state.dailyStreak = 1;
          }
          state.lastStudyDate = new Date().toISOString();
        }
      }
    },
    
    updateProgress: (state, action: PayloadAction<Partial<LearningProgress> & { courseId: string }>) => {
      const { courseId, ...updates } = action.payload;
      const progressIndex = state.progress.findIndex(p => p.courseId === courseId);
      
      if (progressIndex >= 0) {
        state.progress[progressIndex] = { ...state.progress[progressIndex], ...updates };
      }
    },
    
    unlockAchievement: (state, action: PayloadAction<string>) => {
      const achievementIndex = state.achievements.findIndex(a => a.id === action.payload);
      if (achievementIndex >= 0 && !state.achievements[achievementIndex].unlockedAt) {
        state.achievements[achievementIndex].unlockedAt = new Date().toISOString();
        state.achievements[achievementIndex].progress = state.achievements[achievementIndex].maxProgress;
      }
    },
    
    updateAchievementProgress: (state, action: PayloadAction<{ id: string; progress: number }>) => {
      const { id, progress } = action.payload;
      const achievementIndex = state.achievements.findIndex(a => a.id === id);
      
      if (achievementIndex >= 0) {
        state.achievements[achievementIndex].progress = Math.min(
          progress,
          state.achievements[achievementIndex].maxProgress
        );
        
        // 自动解锁成就
        if (progress >= state.achievements[achievementIndex].maxProgress && !state.achievements[achievementIndex].unlockedAt) {
          state.achievements[achievementIndex].unlockedAt = new Date().toISOString();
        }
      }
    },
    
    addAchievement: (state, action: PayloadAction<Achievement>) => {
      const exists = state.achievements.find(a => a.id === action.payload.id);
      if (!exists) {
        state.achievements.push(action.payload);
      }
    },
    
    resetDailyStreak: (state) => {
      state.dailyStreak = 0;
    },
    
    addStudyTime: (state, action: PayloadAction<number>) => {
      state.totalStudyTime += action.payload;
    },
    
    resetCourse: (state, action: PayloadAction<string>) => {
      const progressIndex = state.progress.findIndex(p => p.courseId === action.payload);
      if (progressIndex >= 0) {
        state.progress[progressIndex] = {
          ...state.progress[progressIndex],
          completedLessons: [],
          progress: 0,
          timeSpent: 0,
          startDate: new Date().toISOString(),
          lastAccessDate: new Date().toISOString(),
        };
        
        const course = state.courses.find(c => c.id === action.payload);
        if (course && course.lessons.length > 0) {
          state.progress[progressIndex].currentLesson = course.lessons[0].id;
        }
      }
    },
  },
});

export const {
  setCourses,
  setCurrentCourse,
  startCourse,
  completeLesson,
  updateProgress,
  unlockAchievement,
  updateAchievementProgress,
  addAchievement,
  resetDailyStreak,
  addStudyTime,
  resetCourse,
} = learningSlice.actions;

export default learningSlice.reducer;
