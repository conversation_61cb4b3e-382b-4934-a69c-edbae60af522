import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { User, UserPreferences } from '../../types';

interface UserState {
  currentUser: User | null;
  isAuthenticated: boolean;
  preferences: UserPreferences;
  isLoading: boolean;
}

const initialState: UserState = {
  currentUser: null,
  isAuthenticated: false,
  preferences: {
    theme: 'light',
    notifications: true,
    soundEffects: true,
    hapticFeedback: true,
    defaultTarotDeck: 'rider-waite',
    favoriteOracleDeck: 'angel-cards',
  },
  isLoading: false,
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<User>) => {
      state.currentUser = action.payload;
      state.isAuthenticated = true;
    },
    
    clearUser: (state) => {
      state.currentUser = null;
      state.isAuthenticated = false;
    },
    
    updateUserProfile: (state, action: PayloadAction<Partial<User>>) => {
      if (state.currentUser) {
        state.currentUser = { ...state.currentUser, ...action.payload };
      }
    },
    
    updatePreferences: (state, action: PayloadAction<Partial<UserPreferences>>) => {
      state.preferences = { ...state.preferences, ...action.payload };
    },
    
    incrementExperience: (state, action: PayloadAction<number>) => {
      if (state.currentUser) {
        state.currentUser.experience += action.payload;
        
        // 简单的等级计算：每1000经验升一级
        const newLevel = Math.floor(state.currentUser.experience / 1000) + 1;
        if (newLevel > state.currentUser.level) {
          state.currentUser.level = newLevel;
        }
      }
    },
    
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
  },
});

export const {
  setUser,
  clearUser,
  updateUserProfile,
  updatePreferences,
  incrementExperience,
  setLoading,
} = userSlice.actions;

export default userSlice.reducer;
