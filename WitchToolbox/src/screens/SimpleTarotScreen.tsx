import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { Spacing, FontSizes, BorderRadius } from '../constants/dimensions';
import { getRandomTarotCard, drawCards, getAllTarotCards } from '../utils/cardUtils';
import { TarotCard } from '../types';

export default function SimpleTarotScreen({ navigation }: any) {
  const [step, setStep] = useState<'select' | 'draw' | 'result'>('select');
  const [selectedSpread, setSelectedSpread] = useState<string>('');
  const [drawnCards, setDrawnCards] = useState<TarotCard[]>([]);

  const spreads = [
    { id: 'single', name: '单张抽牌', description: '简单的日常指引', count: 1 },
    { id: 'three', name: '三张牌阵', description: '过去、现在、未来', count: 3 },
    { id: 'love', name: '爱情牌阵', description: '感情问题专用', count: 3 },
  ];

  const handleSpreadSelect = (spreadId: string) => {
    setSelectedSpread(spreadId);
    setStep('draw');
  };

  const handleDrawCards = () => {
    const spread = spreads.find(s => s.id === selectedSpread);
    if (spread) {
      const cards = drawCards(getAllTarotCards(), spread.count);
      setDrawnCards(cards);
      setStep('result');
    }
  };

  const handleNewReading = () => {
    setStep('select');
    setSelectedSpread('');
    setDrawnCards([]);
  };

  const renderSpreadSelection = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>选择塔罗牌阵</Text>
      <Text style={styles.stepSubtitle}>选择适合你问题的牌阵类型</Text>
      
      <ScrollView style={styles.spreadsContainer}>
        {spreads.map((spread) => (
          <TouchableOpacity
            key={spread.id}
            style={styles.spreadCard}
            onPress={() => handleSpreadSelect(spread.id)}
          >
            <LinearGradient
              colors={Colors.gradients.mystical}
              style={styles.spreadGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <View style={styles.spreadHeader}>
                <Text style={styles.spreadName}>{spread.name}</Text>
                <Text style={styles.spreadCount}>{spread.count}张牌</Text>
              </View>
              <Text style={styles.spreadDescription}>{spread.description}</Text>
            </LinearGradient>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderCardDrawing = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>抽取塔罗牌</Text>
      <Text style={styles.stepSubtitle}>静心冥想你的问题，然后点击抽牌</Text>
      
      <View style={styles.drawContainer}>
        <TouchableOpacity style={styles.drawButton} onPress={handleDrawCards}>
          <LinearGradient
            colors={Colors.gradients.cosmic}
            style={styles.drawGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <Ionicons name="sparkles" size={48} color={Colors.neutral.white} />
            <Text style={styles.drawText}>抽牌</Text>
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderResults = () => (
    <ScrollView style={styles.stepContainer}>
      <Text style={styles.stepTitle}>占卜结果</Text>
      
      <View style={styles.cardsContainer}>
        {drawnCards.map((card, index) => (
          <View key={index} style={styles.cardResult}>
            <LinearGradient
              colors={Colors.gradients.golden}
              style={styles.cardGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Text style={styles.cardName}>{card.name}</Text>
              <Text style={styles.cardEnglish}>{card.englishName}</Text>
              <Text style={styles.cardType}>
                {card.type === 'major' ? '大阿卡纳' : '小阿卡纳'}
              </Text>
            </LinearGradient>
            
            <View style={styles.cardMeaning}>
              <Text style={styles.meaningTitle}>正位含义：</Text>
              <Text style={styles.meaningText}>
                {card.meanings.upright.slice(0, 3).join('、')}
              </Text>
              
              <Text style={styles.descriptionTitle}>描述：</Text>
              <Text style={styles.descriptionText}>{card.description}</Text>
            </View>
          </View>
        ))}
      </View>

      <View style={styles.actionButtons}>
        <TouchableOpacity style={styles.actionButton} onPress={handleNewReading}>
          <Text style={styles.actionButtonText}>重新占卜</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton}>
          <Text style={styles.actionButtonText}>保存记录</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );

  const renderCurrentStep = () => {
    switch (step) {
      case 'select':
        return renderSpreadSelection();
      case 'draw':
        return renderCardDrawing();
      case 'result':
        return renderResults();
      default:
        return renderSpreadSelection();
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {renderCurrentStep()}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  stepContainer: {
    flex: 1,
    paddingHorizontal: Spacing.screenPadding,
    paddingVertical: Spacing.lg,
  },
  stepTitle: {
    fontSize: FontSizes.xxl,
    fontWeight: '700',
    color: Colors.light.text,
    textAlign: 'center',
    marginBottom: Spacing.sm,
  },
  stepSubtitle: {
    fontSize: FontSizes.md,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    marginBottom: Spacing.xl,
    lineHeight: 22,
  },
  spreadsContainer: {
    flex: 1,
  },
  spreadCard: {
    marginBottom: Spacing.md,
    borderRadius: BorderRadius.card,
    overflow: 'hidden',
  },
  spreadGradient: {
    padding: Spacing.md,
  },
  spreadHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  spreadName: {
    fontSize: FontSizes.lg,
    fontWeight: '600',
    color: Colors.neutral.white,
  },
  spreadCount: {
    fontSize: FontSizes.sm,
    color: Colors.neutral.white,
    opacity: 0.9,
  },
  spreadDescription: {
    fontSize: FontSizes.md,
    color: Colors.neutral.white,
    opacity: 0.9,
  },
  drawContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  drawButton: {
    width: 200,
    height: 200,
    borderRadius: 100,
    overflow: 'hidden',
  },
  drawGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  drawText: {
    fontSize: FontSizes.lg,
    fontWeight: '600',
    color: Colors.neutral.white,
    marginTop: Spacing.sm,
  },
  cardsContainer: {
    marginBottom: Spacing.xl,
  },
  cardResult: {
    marginBottom: Spacing.lg,
  },
  cardGradient: {
    padding: Spacing.md,
    borderRadius: BorderRadius.card,
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  cardName: {
    fontSize: FontSizes.lg,
    fontWeight: '600',
    color: Colors.neutral.white,
    marginBottom: Spacing.xs,
  },
  cardEnglish: {
    fontSize: FontSizes.md,
    color: Colors.neutral.white,
    opacity: 0.9,
    marginBottom: Spacing.xs,
  },
  cardType: {
    fontSize: FontSizes.sm,
    color: Colors.neutral.white,
    opacity: 0.8,
  },
  cardMeaning: {
    backgroundColor: Colors.light.card,
    padding: Spacing.md,
    borderRadius: BorderRadius.card,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  meaningTitle: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: Spacing.xs,
  },
  meaningText: {
    fontSize: FontSizes.md,
    color: Colors.light.textSecondary,
    marginBottom: Spacing.sm,
  },
  descriptionTitle: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: Spacing.xs,
  },
  descriptionText: {
    fontSize: FontSizes.md,
    color: Colors.light.textSecondary,
    lineHeight: 22,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 1,
    backgroundColor: Colors.primary.purple,
    padding: Spacing.md,
    borderRadius: BorderRadius.button,
    alignItems: 'center',
    marginHorizontal: Spacing.xs,
  },
  actionButtonText: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: Colors.neutral.white,
  },
});
