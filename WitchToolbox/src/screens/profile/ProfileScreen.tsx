import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import { useAppSelector } from '../../store';
import { Colors } from '../../constants/colors';
import { Spacing, FontSizes } from '../../constants/dimensions';

export default function ProfileScreen() {
  const theme = useAppSelector(state => state.app.theme);
  const isDark = theme === 'dark';

  const styles = createStyles(isDark);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>个人中心</Text>
        <Text style={styles.subtitle}>功能开发中...</Text>
      </View>
    </SafeAreaView>
  );
}

const createStyles = (isDark: boolean) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: isDark ? Colors.dark.background : Colors.light.background,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Spacing.screenPadding,
  },
  title: {
    fontSize: FontSizes.xxl,
    fontWeight: '700',
    color: isDark ? Colors.dark.text : Colors.light.text,
    marginBottom: Spacing.sm,
  },
  subtitle: {
    fontSize: FontSizes.md,
    color: isDark ? Colors.dark.textSecondary : Colors.light.textSecondary,
  },
});
