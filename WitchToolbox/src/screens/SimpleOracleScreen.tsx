import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { Spacing, FontSizes, BorderRadius } from '../constants/dimensions';
import { getRandomOracleCard } from '../utils/cardUtils';
import { OracleCard } from '../types';

export default function SimpleOracleScreen({ navigation }: any) {
  const [step, setStep] = useState<'select' | 'draw' | 'result'>('select');
  const [selectedDeck, setSelectedDeck] = useState<string>('');
  const [drawnCard, setDrawnCard] = useState<OracleCard | null>(null);

  const decks = [
    {
      id: 'angel-cards',
      name: '天使卡',
      description: '接收天使的神圣指引和祝福',
      gradient: Colors.gradients.golden,
      icon: 'star',
    },
    {
      id: 'unicorn-cards',
      name: '独角兽卡',
      description: '连接纯真与魔法的能量',
      gradient: Colors.gradients.aurora,
      icon: 'sparkles',
    },
    {
      id: 'goddess-cards',
      name: '女神卡',
      description: '唤醒内在的女性神圣力量',
      gradient: Colors.gradients.sunset,
      icon: 'moon',
    },
  ];

  const handleDeckSelect = (deckId: string) => {
    setSelectedDeck(deckId);
    setStep('draw');
  };

  const handleDrawCard = () => {
    const card = getRandomOracleCard(selectedDeck);
    setDrawnCard(card);
    setStep('result');
  };

  const handleNewReading = () => {
    setStep('select');
    setSelectedDeck('');
    setDrawnCard(null);
  };

  const renderDeckSelection = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>选择神谕卡组</Text>
      <Text style={styles.stepSubtitle}>
        每个卡组都有独特的能量和信息，选择最吸引你的那一个
      </Text>
      
      <ScrollView style={styles.decksContainer}>
        {decks.map((deck) => (
          <TouchableOpacity
            key={deck.id}
            style={styles.deckCard}
            onPress={() => handleDeckSelect(deck.id)}
          >
            <LinearGradient
              colors={deck.gradient}
              style={styles.deckGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <View style={styles.deckHeader}>
                <Ionicons 
                  name={deck.icon as any} 
                  size={32} 
                  color={Colors.neutral.white} 
                />
                <Text style={styles.deckName}>{deck.name}</Text>
              </View>
              <Text style={styles.deckDescription}>{deck.description}</Text>
            </LinearGradient>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderCardDrawing = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>抽取神谕卡</Text>
      <Text style={styles.stepSubtitle}>
        静心冥想，让宇宙为你选择最适合的信息
      </Text>
      
      <View style={styles.drawContainer}>
        <TouchableOpacity style={styles.drawButton} onPress={handleDrawCard}>
          <LinearGradient
            colors={Colors.gradients.mystical}
            style={styles.drawGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <Ionicons name="star" size={48} color={Colors.neutral.white} />
            <Text style={styles.drawText}>抽取神谕卡</Text>
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderResult = () => (
    <ScrollView style={styles.stepContainer}>
      <Text style={styles.stepTitle}>你的神谕卡</Text>
      
      {drawnCard && (
        <>
          <View style={styles.cardContainer}>
            <LinearGradient
              colors={Colors.gradients.golden}
              style={styles.cardGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Text style={styles.cardTitle}>{drawnCard.name}</Text>
            </LinearGradient>
          </View>

          <View style={styles.interpretationContainer}>
            <View style={styles.interpretationCard}>
              <Text style={styles.sectionTitle}>卡牌含义</Text>
              <Text style={styles.sectionText}>{drawnCard.meaning}</Text>
            </View>

            <View style={styles.interpretationCard}>
              <Text style={styles.sectionTitle}>指引建议</Text>
              <Text style={styles.sectionText}>{drawnCard.guidance}</Text>
            </View>

            <View style={styles.interpretationCard}>
              <Text style={styles.sectionTitle}>正面肯定</Text>
              <Text style={styles.affirmationText}>{drawnCard.affirmation}</Text>
            </View>

            <View style={styles.keywordsContainer}>
              <Text style={styles.keywordsTitle}>关键词：</Text>
              <View style={styles.keywordsList}>
                {drawnCard.keywords.map((keyword, index) => (
                  <View key={index} style={styles.keywordTag}>
                    <Text style={styles.keywordText}>{keyword}</Text>
                  </View>
                ))}
              </View>
            </View>
          </View>

          <View style={styles.actionButtons}>
            <TouchableOpacity style={styles.actionButton} onPress={handleNewReading}>
              <Text style={styles.actionButtonText}>重新抽卡</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <Text style={styles.actionButtonText}>保存记录</Text>
            </TouchableOpacity>
          </View>
        </>
      )}
    </ScrollView>
  );

  const renderCurrentStep = () => {
    switch (step) {
      case 'select':
        return renderDeckSelection();
      case 'draw':
        return renderCardDrawing();
      case 'result':
        return renderResult();
      default:
        return renderDeckSelection();
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {renderCurrentStep()}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  stepContainer: {
    flex: 1,
    paddingHorizontal: Spacing.screenPadding,
    paddingVertical: Spacing.lg,
  },
  stepTitle: {
    fontSize: FontSizes.xxl,
    fontWeight: '700',
    color: Colors.light.text,
    textAlign: 'center',
    marginBottom: Spacing.sm,
  },
  stepSubtitle: {
    fontSize: FontSizes.md,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    marginBottom: Spacing.xl,
    lineHeight: 22,
  },
  decksContainer: {
    flex: 1,
  },
  deckCard: {
    marginBottom: Spacing.md,
    borderRadius: BorderRadius.card,
    overflow: 'hidden',
  },
  deckGradient: {
    padding: Spacing.lg,
  },
  deckHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  deckName: {
    fontSize: FontSizes.lg,
    fontWeight: '600',
    color: Colors.neutral.white,
    marginLeft: Spacing.sm,
  },
  deckDescription: {
    fontSize: FontSizes.md,
    color: Colors.neutral.white,
    opacity: 0.9,
    lineHeight: 20,
  },
  drawContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  drawButton: {
    width: 200,
    height: 200,
    borderRadius: 100,
    overflow: 'hidden',
  },
  drawGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  drawText: {
    fontSize: FontSizes.lg,
    fontWeight: '600',
    color: Colors.neutral.white,
    marginTop: Spacing.sm,
  },
  cardContainer: {
    alignItems: 'center',
    marginBottom: Spacing.xl,
  },
  cardGradient: {
    width: 200,
    height: 120,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: BorderRadius.card,
  },
  cardTitle: {
    fontSize: FontSizes.lg,
    fontWeight: '600',
    color: Colors.neutral.white,
    textAlign: 'center',
  },
  interpretationContainer: {
    marginBottom: Spacing.xl,
  },
  interpretationCard: {
    backgroundColor: Colors.light.card,
    padding: Spacing.md,
    borderRadius: BorderRadius.card,
    borderWidth: 1,
    borderColor: Colors.light.border,
    marginBottom: Spacing.md,
  },
  sectionTitle: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: Spacing.xs,
  },
  sectionText: {
    fontSize: FontSizes.md,
    color: Colors.light.textSecondary,
    lineHeight: 22,
  },
  affirmationText: {
    fontSize: FontSizes.md,
    color: Colors.primary.purple,
    fontWeight: '500',
    lineHeight: 22,
    fontStyle: 'italic',
  },
  keywordsContainer: {
    marginBottom: Spacing.lg,
  },
  keywordsTitle: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: Spacing.sm,
  },
  keywordsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  keywordTag: {
    backgroundColor: Colors.primary.purple + '20',
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
    marginRight: Spacing.xs,
    marginBottom: Spacing.xs,
  },
  keywordText: {
    fontSize: FontSizes.sm,
    color: Colors.primary.purple,
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 1,
    backgroundColor: Colors.primary.purple,
    padding: Spacing.md,
    borderRadius: BorderRadius.button,
    alignItems: 'center',
    marginHorizontal: Spacing.xs,
  },
  actionButtonText: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: Colors.neutral.white,
  },
});
