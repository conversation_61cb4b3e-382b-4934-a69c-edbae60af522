import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { Spacing, FontSizes, BorderRadius } from '../constants/dimensions';
import { getRandomTarotCard, getRandomOracleCard } from '../utils/cardUtils';

export default function SimpleDivinationScreen({ navigation }: any) {
  const [selectedCard, setSelectedCard] = useState<any>(null);

  const handleTarotReading = () => {
    navigation.navigate('TarotReading');
  };

  const handleOracleReading = () => {
    navigation.navigate('OracleReading');
  };

  const handleQuickTarot = () => {
    try {
      const card = getRandomTarotCard();
      setSelectedCard(card);
      Alert.alert(
        '快速塔罗',
        `你抽到了：${card.name}\n\n含义：${card.meanings.upright[0]}\n\n${card.description}`,
        [{ text: '确定' }]
      );
    } catch (error) {
      Alert.alert('错误', '抽牌失败，请重试');
    }
  };

  const handleQuickOracle = () => {
    try {
      const card = getRandomOracleCard();
      setSelectedCard(card);
      Alert.alert(
        '快速神谕',
        `你抽到了：${card.name}\n\n${card.meaning}`,
        [{ text: '确定' }]
      );
    } catch (error) {
      Alert.alert('错误', '抽牌失败，请重试');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>选择占卜方式</Text>
        <Text style={styles.subtitle}>
          静心冥想你的问题，然后选择一种占卜方式
        </Text>

        <View style={styles.optionsContainer}>
          <TouchableOpacity
            style={styles.optionCard}
            onPress={handleTarotReading}
          >
            <LinearGradient
              colors={Colors.gradients.mystical}
              style={styles.optionGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Ionicons name="sparkles" size={48} color={Colors.neutral.white} />
              <Text style={styles.optionTitle}>塔罗占卜</Text>
              <Text style={styles.optionDescription}>
                通过78张塔罗牌获得人生指引
              </Text>
            </LinearGradient>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.optionCard}
            onPress={handleOracleReading}
          >
            <LinearGradient
              colors={Colors.gradients.golden}
              style={styles.optionGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Ionicons name="star" size={48} color={Colors.neutral.white} />
              <Text style={styles.optionTitle}>神谕卡</Text>
              <Text style={styles.optionDescription}>
                接收天使的神圣信息和祝福
              </Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>

        <View style={styles.quickSection}>
          <Text style={styles.quickTitle}>快速占卜</Text>
          <View style={styles.quickButtons}>
            <TouchableOpacity style={styles.quickButton} onPress={handleQuickTarot}>
              <Text style={styles.quickButtonText}>快速塔罗</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.quickButton} onPress={handleQuickOracle}>
              <Text style={styles.quickButtonText}>快速神谕</Text>
            </TouchableOpacity>
          </View>
        </View>

        {selectedCard && (
          <View style={styles.resultSection}>
            <Text style={styles.resultTitle}>最近抽取的卡牌</Text>
            <View style={styles.resultCard}>
              <Text style={styles.cardName}>
                {selectedCard.name || selectedCard.englishName}
              </Text>
              <Text style={styles.cardType}>
                {selectedCard.type === 'major' ? '大阿卡纳' : 
                 selectedCard.type === 'minor' ? '小阿卡纳' : '神谕卡'}
              </Text>
            </View>
          </View>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  content: {
    flex: 1,
    paddingHorizontal: Spacing.screenPadding,
    paddingVertical: Spacing.lg,
  },
  title: {
    fontSize: FontSizes.xxl,
    fontWeight: '700',
    color: Colors.light.text,
    textAlign: 'center',
    marginBottom: Spacing.sm,
  },
  subtitle: {
    fontSize: FontSizes.md,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    marginBottom: Spacing.xl,
    lineHeight: 22,
  },
  optionsContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  optionCard: {
    marginBottom: Spacing.lg,
    borderRadius: BorderRadius.card,
    overflow: 'hidden',
  },
  optionGradient: {
    padding: Spacing.xl,
    alignItems: 'center',
    minHeight: 150,
    justifyContent: 'center',
  },
  optionTitle: {
    fontSize: FontSizes.lg,
    fontWeight: '600',
    color: Colors.neutral.white,
    marginTop: Spacing.md,
    marginBottom: Spacing.sm,
  },
  optionDescription: {
    fontSize: FontSizes.md,
    color: Colors.neutral.white,
    opacity: 0.9,
    textAlign: 'center',
    lineHeight: 20,
  },
  resultSection: {
    marginTop: Spacing.xl,
  },
  resultTitle: {
    fontSize: FontSizes.lg,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: Spacing.md,
  },
  resultCard: {
    backgroundColor: Colors.light.card,
    padding: Spacing.md,
    borderRadius: BorderRadius.card,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  cardName: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: Spacing.xs,
  },
  cardType: {
    fontSize: FontSizes.sm,
    color: Colors.light.textSecondary,
  },
  quickSection: {
    marginTop: Spacing.xl,
  },
  quickTitle: {
    fontSize: FontSizes.lg,
    fontWeight: '600',
    color: Colors.light.text,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  quickButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  quickButton: {
    flex: 1,
    backgroundColor: Colors.primary.purple,
    padding: Spacing.md,
    borderRadius: BorderRadius.button,
    alignItems: 'center',
    marginHorizontal: Spacing.xs,
  },
  quickButtonText: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: Colors.neutral.white,
  },
});
