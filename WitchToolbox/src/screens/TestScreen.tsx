import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useAppSelector, useAppDispatch } from '../store';
import { Colors } from '../constants/colors';
import { Spacing, FontSizes, BorderRadius } from '../constants/dimensions';
import { getRandomTarotCard, getRandomOracleCard } from '../utils/cardUtils';

export default function TestScreen() {
  const [testResults, setTestResults] = useState<string[]>([]);
  const dispatch = useAppDispatch();
  const user = useAppSelector(state => state.user.currentUser);

  const runTest = (testName: string, testFn: () => void) => {
    try {
      testFn();
      const result = `✅ ${testName}: 通过`;
      setTestResults(prev => [...prev, result]);
      return true;
    } catch (error) {
      const result = `❌ ${testName}: 失败 - ${error}`;
      setTestResults(prev => [...prev, result]);
      return false;
    }
  };

  const runAllTests = () => {
    setTestResults([]);
    
    // 测试塔罗牌数据
    runTest('塔罗牌数据加载', () => {
      const card = getRandomTarotCard();
      if (!card || !card.name || !card.meanings) {
        throw new Error('塔罗牌数据不完整');
      }
    });

    // 测试神谕卡数据
    runTest('神谕卡数据加载', () => {
      const card = getRandomOracleCard();
      if (!card || !card.name || !card.meaning) {
        throw new Error('神谕卡数据不完整');
      }
    });

    // 测试Redux状态
    runTest('Redux状态管理', () => {
      // 这里可以测试dispatch和state
      if (typeof dispatch !== 'function') {
        throw new Error('Redux dispatch不可用');
      }
    });

    // 测试颜色系统
    runTest('颜色系统', () => {
      if (!Colors.primary.purple || !Colors.gradients.mystical) {
        throw new Error('颜色系统不完整');
      }
    });

    Alert.alert('测试完成', '所有测试已运行完毕，请查看结果');
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>应用功能测试</Text>
        
        <TouchableOpacity style={styles.testButton} onPress={runAllTests}>
          <LinearGradient
            colors={Colors.gradients.mystical}
            style={styles.buttonGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <Ionicons name="play" size={24} color={Colors.neutral.white} />
            <Text style={styles.buttonText}>运行所有测试</Text>
          </LinearGradient>
        </TouchableOpacity>

        <View style={styles.resultsContainer}>
          <Text style={styles.resultsTitle}>测试结果：</Text>
          {testResults.map((result, index) => (
            <Text key={index} style={styles.resultText}>
              {result}
            </Text>
          ))}
        </View>

        <View style={styles.infoContainer}>
          <Text style={styles.infoTitle}>应用信息：</Text>
          <Text style={styles.infoText}>• React Native + Expo</Text>
          <Text style={styles.infoText}>• Redux状态管理</Text>
          <Text style={styles.infoText}>• 78张塔罗牌数据</Text>
          <Text style={styles.infoText}>• 15张天使神谕卡</Text>
          <Text style={styles.infoText}>• 类iOS设计风格</Text>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  content: {
    flex: 1,
    paddingHorizontal: Spacing.screenPadding,
    paddingVertical: Spacing.lg,
  },
  title: {
    fontSize: FontSizes.xxl,
    fontWeight: '700',
    color: Colors.light.text,
    textAlign: 'center',
    marginBottom: Spacing.xl,
  },
  testButton: {
    borderRadius: BorderRadius.card,
    overflow: 'hidden',
    marginBottom: Spacing.xl,
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: Spacing.lg,
  },
  buttonText: {
    fontSize: FontSizes.lg,
    fontWeight: '600',
    color: Colors.neutral.white,
    marginLeft: Spacing.sm,
  },
  resultsContainer: {
    backgroundColor: Colors.light.card,
    padding: Spacing.md,
    borderRadius: BorderRadius.card,
    borderWidth: 1,
    borderColor: Colors.light.border,
    marginBottom: Spacing.lg,
    minHeight: 150,
  },
  resultsTitle: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: Spacing.sm,
  },
  resultText: {
    fontSize: FontSizes.sm,
    color: Colors.light.textSecondary,
    marginBottom: Spacing.xs,
    fontFamily: 'monospace',
  },
  infoContainer: {
    backgroundColor: Colors.light.card,
    padding: Spacing.md,
    borderRadius: BorderRadius.card,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  infoTitle: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: Spacing.sm,
  },
  infoText: {
    fontSize: FontSizes.sm,
    color: Colors.light.textSecondary,
    marginBottom: Spacing.xs,
  },
});
