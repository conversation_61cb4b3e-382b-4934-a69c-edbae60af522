import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useAppSelector } from '../store';
import { Colors } from '../constants/colors';
import { Spacing, FontSizes, BorderRadius } from '../constants/dimensions';

export default function SimpleHomeScreen({ navigation }: any) {
  const user = useAppSelector(state => state.user.currentUser);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        {/* 欢迎区域 */}
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeText}>欢迎来到女巫工具箱</Text>
          <Text style={styles.subtitleText}>探索神秘学的奥秘</Text>
        </View>

        {/* 每日神谕卡 */}
        <TouchableOpacity style={styles.dailyCard}>
          <LinearGradient
            colors={Colors.gradients.mystical}
            style={styles.dailyCardGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <Ionicons name="star" size={32} color={Colors.neutral.white} />
            <Text style={styles.dailyCardTitle}>今日神谕</Text>
            <Text style={styles.dailyCardText}>
              点击获取今日的神圣指引
            </Text>
          </LinearGradient>
        </TouchableOpacity>

        {/* 快速功能 */}
        <View style={styles.quickActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('TarotReading')}
          >
            <LinearGradient
              colors={Colors.gradients.golden}
              style={styles.actionGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Ionicons name="sparkles" size={24} color={Colors.neutral.white} />
              <Text style={styles.actionText}>塔罗占卜</Text>
            </LinearGradient>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('OracleReading')}
          >
            <LinearGradient
              colors={Colors.gradients.aurora}
              style={styles.actionGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Ionicons name="moon" size={24} color={Colors.neutral.white} />
              <Text style={styles.actionText}>神谕卡</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>

        {/* 学习进度 */}
        <View style={styles.progressSection}>
          <Text style={styles.sectionTitle}>学习进度</Text>
          <View style={styles.progressCard}>
            <Text style={styles.progressTitle}>塔罗基础课程</Text>
            <View style={styles.progressBar}>
              <View style={[styles.progressFill, { width: '30%' }]} />
            </View>
            <Text style={styles.progressText}>已完成 3/10 课时</Text>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  content: {
    flex: 1,
    paddingHorizontal: Spacing.screenPadding,
    paddingVertical: Spacing.lg,
  },
  welcomeSection: {
    alignItems: 'center',
    marginBottom: Spacing.xl,
  },
  welcomeText: {
    fontSize: FontSizes.xxl,
    fontWeight: '700',
    color: Colors.light.text,
    marginBottom: Spacing.xs,
  },
  subtitleText: {
    fontSize: FontSizes.md,
    color: Colors.light.textSecondary,
  },
  dailyCard: {
    borderRadius: BorderRadius.card,
    overflow: 'hidden',
    marginBottom: Spacing.xl,
  },
  dailyCardGradient: {
    padding: Spacing.lg,
    alignItems: 'center',
  },
  dailyCardTitle: {
    fontSize: FontSizes.lg,
    fontWeight: '600',
    color: Colors.neutral.white,
    marginTop: Spacing.sm,
    marginBottom: Spacing.xs,
  },
  dailyCardText: {
    fontSize: FontSizes.md,
    color: Colors.neutral.white,
    opacity: 0.9,
    textAlign: 'center',
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Spacing.xl,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: Spacing.xs,
    borderRadius: BorderRadius.card,
    overflow: 'hidden',
  },
  actionGradient: {
    padding: Spacing.md,
    alignItems: 'center',
    minHeight: 100,
    justifyContent: 'center',
  },
  actionText: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: Colors.neutral.white,
    marginTop: Spacing.xs,
  },
  progressSection: {
    marginTop: Spacing.lg,
  },
  sectionTitle: {
    fontSize: FontSizes.lg,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: Spacing.md,
  },
  progressCard: {
    backgroundColor: Colors.light.card,
    padding: Spacing.md,
    borderRadius: BorderRadius.card,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  progressTitle: {
    fontSize: FontSizes.md,
    fontWeight: '500',
    color: Colors.light.text,
    marginBottom: Spacing.sm,
  },
  progressBar: {
    height: 6,
    backgroundColor: Colors.light.border,
    borderRadius: 3,
    marginBottom: Spacing.sm,
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.primary.purple,
    borderRadius: 3,
  },
  progressText: {
    fontSize: FontSizes.sm,
    color: Colors.light.textSecondary,
  },
});
