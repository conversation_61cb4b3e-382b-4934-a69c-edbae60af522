import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useAppSelector } from '../../store';
import { Colors } from '../../constants/colors';
import { Spacing, FontSizes, BorderRadius } from '../../constants/dimensions';

export default function DivinationScreen({ navigation }: any) {
  const theme = useAppSelector(state => state.app.theme);
  const isDark = theme === 'dark';

  const styles = createStyles(isDark);

  const divinationTools = [
    {
      id: 'tarot',
      title: '塔罗占卜',
      description: '78张塔罗牌的智慧指引',
      icon: 'sparkles',
      gradient: Colors.gradients.mystical,
      features: ['多种牌阵', '专业解读', '历史记录'],
      onPress: () => navigation.navigate('TarotReading'),
    },
    {
      id: 'oracle',
      title: '神谕卡占卜',
      description: '天使与宇宙的神圣信息',
      icon: 'star',
      gradient: Colors.gradients.golden,
      features: ['5套卡组', '每日神谕', '冥想引导'],
      onPress: () => navigation.navigate('OracleReading'),
    },
    {
      id: 'oh-cards',
      title: 'OH卡心理投射',
      description: '探索潜意识的心理工具',
      icon: 'eye',
      gradient: Colors.gradients.aurora,
      features: ['图像联想', '心理分析', '自我探索'],
      onPress: () => {},
    },
    {
      id: 'numerology',
      title: '数字占卜',
      description: '生命密码与数字能量',
      icon: 'calculator',
      gradient: Colors.gradients.sunset,
      features: ['生命数字', '姓名分析', '运势预测'],
      onPress: () => {},
    },
    {
      id: 'astrology',
      title: '占星学',
      description: '星座与宇宙的奥秘',
      icon: 'planet',
      gradient: Colors.gradients.cosmic,
      features: ['星座运势', '星盘分析', '行星影响'],
      onPress: () => {},
    },
    {
      id: 'iching',
      title: '易经占卜',
      description: '古老东方智慧的指引',
      icon: 'yin-yang',
      gradient: Colors.gradients.primary,
      features: ['六爻占卜', '64卦解读', '变卦分析'],
      onPress: () => {},
    },
  ];

  const renderDivinationTool = (tool: any) => (
    <TouchableOpacity
      key={tool.id}
      style={styles.toolCard}
      onPress={tool.onPress}
      activeOpacity={0.8}
    >
      <LinearGradient
        colors={tool.gradient}
        style={styles.toolGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.toolHeader}>
          <Ionicons 
            name={tool.icon} 
            size={32} 
            color={Colors.neutral.white} 
          />
          <View style={styles.toolTitleContainer}>
            <Text style={styles.toolTitle}>{tool.title}</Text>
            <Text style={styles.toolDescription}>{tool.description}</Text>
          </View>
        </View>
        
        <View style={styles.toolFeatures}>
          {tool.features.map((feature: string, index: number) => (
            <View key={index} style={styles.featureItem}>
              <Ionicons 
                name="checkmark-circle" 
                size={16} 
                color={Colors.neutral.white} 
              />
              <Text style={styles.featureText}>{feature}</Text>
            </View>
          ))}
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>选择占卜工具</Text>
          <Text style={styles.headerSubtitle}>
            探索内在智慧，寻找人生答案
          </Text>
        </View>

        {/* Tools Grid */}
        <View style={styles.toolsContainer}>
          {divinationTools.map(renderDivinationTool)}
        </View>

        {/* Recent Readings */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>最近占卜记录</Text>
            <TouchableOpacity>
              <Text style={styles.seeAllText}>查看全部</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.readingsList}>
            <TouchableOpacity style={styles.readingItem}>
              <View style={styles.readingIcon}>
                <Ionicons 
                  name="sparkles" 
                  size={20} 
                  color={Colors.primary.purple} 
                />
              </View>
              <View style={styles.readingContent}>
                <Text style={styles.readingTitle}>爱情三角牌阵</Text>
                <Text style={styles.readingSubtitle}>关于感情发展的问题</Text>
                <Text style={styles.readingDate}>2小时前</Text>
              </View>
              <Ionicons 
                name="chevron-forward" 
                size={16} 
                color={isDark ? Colors.dark.textSecondary : Colors.light.textSecondary} 
              />
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.readingItem}>
              <View style={styles.readingIcon}>
                <Ionicons 
                  name="star" 
                  size={20} 
                  color={Colors.secondary.emerald} 
                />
              </View>
              <View style={styles.readingContent}>
                <Text style={styles.readingTitle}>天使指引卡</Text>
                <Text style={styles.readingSubtitle}>寻求内心平静的指导</Text>
                <Text style={styles.readingDate}>昨天</Text>
              </View>
              <Ionicons 
                name="chevron-forward" 
                size={16} 
                color={isDark ? Colors.dark.textSecondary : Colors.light.textSecondary} 
              />
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.readingItem}>
              <View style={styles.readingIcon}>
                <Ionicons 
                  name="calculator" 
                  size={20} 
                  color={Colors.primary.gold} 
                />
              </View>
              <View style={styles.readingContent}>
                <Text style={styles.readingTitle}>生命密码分析</Text>
                <Text style={styles.readingSubtitle}>个人数字能量解读</Text>
                <Text style={styles.readingDate}>3天前</Text>
              </View>
              <Ionicons 
                name="chevron-forward" 
                size={16} 
                color={isDark ? Colors.dark.textSecondary : Colors.light.textSecondary} 
              />
            </TouchableOpacity>
          </View>
        </View>

        {/* Tips */}
        <View style={styles.section}>
          <View style={styles.tipCard}>
            <Ionicons 
              name="bulb" 
              size={24} 
              color={Colors.primary.purple} 
            />
            <View style={styles.tipContent}>
              <Text style={styles.tipTitle}>占卜小贴士</Text>
              <Text style={styles.tipText}>
                在占卜前，请保持内心平静，专注于你想要了解的问题。诚心诚意地提问，会得到更准确的指引。
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const createStyles = (isDark: boolean) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: isDark ? Colors.dark.background : Colors.light.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: Spacing.screenPadding,
    paddingVertical: Spacing.lg,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: FontSizes.xxl,
    fontWeight: '700',
    color: isDark ? Colors.dark.text : Colors.light.text,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: FontSizes.md,
    color: isDark ? Colors.dark.textSecondary : Colors.light.textSecondary,
    textAlign: 'center',
    marginTop: Spacing.sm,
  },
  toolsContainer: {
    paddingHorizontal: Spacing.screenPadding,
  },
  toolCard: {
    marginBottom: Spacing.md,
    borderRadius: BorderRadius.card,
    overflow: 'hidden',
  },
  toolGradient: {
    padding: Spacing.md,
  },
  toolHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  toolTitleContainer: {
    flex: 1,
    marginLeft: Spacing.sm,
  },
  toolTitle: {
    fontSize: FontSizes.lg,
    fontWeight: '600',
    color: Colors.neutral.white,
  },
  toolDescription: {
    fontSize: FontSizes.sm,
    color: Colors.neutral.white,
    opacity: 0.9,
    marginTop: 2,
  },
  toolFeatures: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: Spacing.md,
    marginBottom: Spacing.xs,
  },
  featureText: {
    fontSize: FontSizes.sm,
    color: Colors.neutral.white,
    marginLeft: Spacing.xs,
    opacity: 0.9,
  },
  section: {
    marginTop: Spacing.xl,
    paddingHorizontal: Spacing.screenPadding,
    marginBottom: Spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  sectionTitle: {
    fontSize: FontSizes.lg,
    fontWeight: '600',
    color: isDark ? Colors.dark.text : Colors.light.text,
  },
  seeAllText: {
    fontSize: FontSizes.sm,
    color: Colors.primary.purple,
    fontWeight: '500',
  },
  readingsList: {
    backgroundColor: isDark ? Colors.dark.card : Colors.light.card,
    borderRadius: BorderRadius.card,
    borderWidth: 1,
    borderColor: isDark ? Colors.dark.border : Colors.light.border,
  },
  readingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: isDark ? Colors.dark.border : Colors.light.border,
  },
  readingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: isDark ? Colors.dark.background : Colors.light.background,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.sm,
  },
  readingContent: {
    flex: 1,
  },
  readingTitle: {
    fontSize: FontSizes.md,
    fontWeight: '500',
    color: isDark ? Colors.dark.text : Colors.light.text,
  },
  readingSubtitle: {
    fontSize: FontSizes.sm,
    color: isDark ? Colors.dark.textSecondary : Colors.light.textSecondary,
    marginTop: 2,
  },
  readingDate: {
    fontSize: FontSizes.xs,
    color: isDark ? Colors.dark.textSecondary : Colors.light.textSecondary,
    marginTop: 4,
  },
  tipCard: {
    flexDirection: 'row',
    backgroundColor: isDark ? Colors.dark.card : Colors.light.card,
    padding: Spacing.md,
    borderRadius: BorderRadius.card,
    borderWidth: 1,
    borderColor: isDark ? Colors.dark.border : Colors.light.border,
  },
  tipContent: {
    flex: 1,
    marginLeft: Spacing.sm,
  },
  tipTitle: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: isDark ? Colors.dark.text : Colors.light.text,
    marginBottom: Spacing.xs,
  },
  tipText: {
    fontSize: FontSizes.sm,
    color: isDark ? Colors.dark.textSecondary : Colors.light.textSecondary,
    lineHeight: 20,
  },
});
