import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useAppSelector } from '../../store';
import { Colors } from '../../constants/colors';
import { Spacing, FontSizes, BorderRadius, CardSizes } from '../../constants/dimensions';
import { Button, Card, MysticalLoading } from '../../components/common';
import { getRandomOracleCard } from '../../utils/cardUtils';
import { OracleCard } from '../../types';

export default function OracleReadingScreen({ navigation }: any) {
  const theme = useAppSelector(state => state.app.theme);
  const isDark = theme === 'dark';

  const [step, setStep] = useState<'select-deck' | 'draw-card' | 'reveal-card'>('select-deck');
  const [selectedDeck, setSelectedDeck] = useState<string>('');
  const [drawnCard, setDrawnCard] = useState<OracleCard | null>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [isRevealed, setIsRevealed] = useState(false);

  const styles = createStyles(isDark);

  const oracleDecks = [
    {
      id: 'angel-cards',
      name: '天使卡',
      description: '接收天使的神圣指引和祝福',
      gradient: Colors.gradients.golden,
      icon: 'star',
    },
    {
      id: 'unicorn-cards',
      name: '独角兽卡',
      description: '连接纯真与魔法的能量',
      gradient: Colors.gradients.aurora,
      icon: 'sparkles',
    },
    {
      id: 'goddess-cards',
      name: '女神卡',
      description: '唤醒内在的女性神圣力量',
      gradient: Colors.gradients.sunset,
      icon: 'moon',
    },
  ];

  const handleDeckSelect = (deckId: string) => {
    setSelectedDeck(deckId);
    setStep('draw-card');
  };

  const handleDrawCard = async () => {
    setIsDrawing(true);

    // 模拟抽卡过程
    setTimeout(() => {
      const card = getRandomOracleCard(selectedDeck);
      setDrawnCard(card);
      setIsDrawing(false);
      setStep('reveal-card');
    }, 2000);
  };

  const handleCardReveal = () => {
    setIsRevealed(true);
  };

  const handleNewReading = () => {
    setStep('select-deck');
    setSelectedDeck('');
    setDrawnCard(null);
    setIsRevealed(false);
  };

  const renderDeckSelection = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>选择神谕卡组</Text>
      <Text style={styles.stepSubtitle}>
        每个卡组都有独特的能量和信息，选择最吸引你的那一个
      </Text>

      <ScrollView style={styles.decksContainer} showsVerticalScrollIndicator={false}>
        {oracleDecks.map((deck) => (
          <TouchableOpacity
            key={deck.id}
            style={styles.deckCard}
            onPress={() => handleDeckSelect(deck.id)}
            activeOpacity={0.8}
          >
            <LinearGradient
              colors={deck.gradient}
              style={styles.deckGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <View style={styles.deckHeader}>
                <Ionicons
                  name={deck.icon as any}
                  size={32}
                  color={Colors.neutral.white}
                />
                <Text style={styles.deckName}>{deck.name}</Text>
              </View>
              <Text style={styles.deckDescription}>{deck.description}</Text>
            </LinearGradient>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderCardDrawing = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>抽取神谕卡</Text>
      <Text style={styles.stepSubtitle}>
        静心冥想，让宇宙为你选择最适合的信息
      </Text>

      <View style={styles.drawingContainer}>
        {isDrawing ? (
          <MysticalLoading
            visible={true}
            text="正在为你抽取神谕卡..."
            size="large"
            theme={theme}
          />
        ) : (
          <Button
            title="抽取神谕卡"
            onPress={handleDrawCard}
            variant="gradient"
            gradient={Colors.gradients.mystical}
            size="large"
            style={styles.drawButton}
            icon="star"
          />
        )}
      </View>
    </View>
  );

  const renderCardReveal = () => (
    <ScrollView style={styles.stepContainer} showsVerticalScrollIndicator={false}>
      <Text style={styles.stepTitle}>你的神谕卡</Text>

      <View style={styles.cardContainer}>
        <TouchableOpacity
          style={styles.oracleCard}
          onPress={!isRevealed ? handleCardReveal : undefined}
          activeOpacity={isRevealed ? 1 : 0.8}
        >
          {isRevealed && drawnCard ? (
            <LinearGradient
              colors={Colors.gradients.golden}
              style={styles.revealedCardGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Text style={styles.cardTitle}>{drawnCard.name}</Text>
            </LinearGradient>
          ) : (
            <LinearGradient
              colors={Colors.gradients.cosmic}
              style={styles.hiddenCardGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Ionicons name="star" size={48} color={Colors.neutral.white} />
              <Text style={styles.tapToReveal}>点击翻开</Text>
            </LinearGradient>
          )}
        </TouchableOpacity>
      </View>

      {isRevealed && drawnCard && (
        <View style={styles.interpretationContainer}>
          <Card style={styles.interpretationCard} theme={theme}>
            <Text style={styles.sectionTitle}>卡牌含义</Text>
            <Text style={styles.sectionText}>{drawnCard.meaning}</Text>
          </Card>

          <Card style={styles.interpretationCard} theme={theme}>
            <Text style={styles.sectionTitle}>指引建议</Text>
            <Text style={styles.sectionText}>{drawnCard.guidance}</Text>
          </Card>

          <Card style={styles.interpretationCard} theme={theme}>
            <Text style={styles.sectionTitle}>正面肯定</Text>
            <Text style={styles.affirmationText}>{drawnCard.affirmation}</Text>
          </Card>

          <View style={styles.keywordsContainer}>
            <Text style={styles.keywordsTitle}>关键词：</Text>
            <View style={styles.keywordsList}>
              {drawnCard.keywords.map((keyword, index) => (
                <View key={index} style={styles.keywordTag}>
                  <Text style={styles.keywordText}>{keyword}</Text>
                </View>
              ))}
            </View>
          </View>

          <View style={styles.actionButtons}>
            <Button
              title="重新抽卡"
              onPress={handleNewReading}
              variant="outline"
              style={styles.actionButton}
            />
            <Button
              title="保存记录"
              onPress={() => {}}
              variant="gradient"
              gradient={Colors.gradients.golden}
              style={styles.actionButton}
            />
          </View>
        </View>
      )}
    </ScrollView>
  );

  const renderCurrentStep = () => {
    switch (step) {
      case 'select-deck':
        return renderDeckSelection();
      case 'draw-card':
        return renderCardDrawing();
      case 'reveal-card':
        return renderCardReveal();
      default:
        return renderDeckSelection();
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons
            name="chevron-back"
            size={24}
            color={isDark ? Colors.dark.text : Colors.light.text}
          />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>神谕卡占卜</Text>
        <View style={styles.placeholder} />
      </View>

      {renderCurrentStep()}
    </SafeAreaView>
  );
}

const createStyles = (isDark: boolean) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: isDark ? Colors.dark.background : Colors.light.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.screenPadding,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: isDark ? Colors.dark.border : Colors.light.border,
  },
  backButton: {
    padding: Spacing.xs,
  },
  headerTitle: {
    fontSize: FontSizes.lg,
    fontWeight: '600',
    color: isDark ? Colors.dark.text : Colors.light.text,
  },
  placeholder: {
    width: 40,
  },
  stepContainer: {
    flex: 1,
    paddingHorizontal: Spacing.screenPadding,
    paddingVertical: Spacing.lg,
  },
  stepTitle: {
    fontSize: FontSizes.xxl,
    fontWeight: '700',
    color: isDark ? Colors.dark.text : Colors.light.text,
    textAlign: 'center',
    marginBottom: Spacing.sm,
  },
  stepSubtitle: {
    fontSize: FontSizes.md,
    color: isDark ? Colors.dark.textSecondary : Colors.light.textSecondary,
    textAlign: 'center',
    marginBottom: Spacing.xl,
    lineHeight: 22,
  },
  decksContainer: {
    flex: 1,
  },
  deckCard: {
    marginBottom: Spacing.md,
    borderRadius: BorderRadius.card,
    overflow: 'hidden',
  },
  deckGradient: {
    padding: Spacing.lg,
  },
  deckHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  deckName: {
    fontSize: FontSizes.lg,
    fontWeight: '600',
    color: Colors.neutral.white,
    marginLeft: Spacing.sm,
  },
  deckDescription: {
    fontSize: FontSizes.md,
    color: Colors.neutral.white,
    opacity: 0.9,
    lineHeight: 20,
  },
  drawingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  drawButton: {
    paddingHorizontal: Spacing.xl,
  },
  cardContainer: {
    alignItems: 'center',
    marginBottom: Spacing.xl,
  },
  oracleCard: {
    width: CardSizes.oracle.width,
    height: CardSizes.oracle.height,
    borderRadius: BorderRadius.card,
    overflow: 'hidden',
  },
  revealedCardGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.md,
  },
  hiddenCardGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.md,
  },
  cardTitle: {
    fontSize: FontSizes.lg,
    fontWeight: '600',
    color: Colors.neutral.white,
    textAlign: 'center',
  },
  tapToReveal: {
    fontSize: FontSizes.sm,
    color: Colors.neutral.white,
    opacity: 0.9,
    marginTop: Spacing.sm,
  },
  interpretationContainer: {
    marginTop: Spacing.lg,
  },
  interpretationCard: {
    marginBottom: Spacing.md,
  },
  sectionTitle: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: isDark ? Colors.dark.text : Colors.light.text,
    marginBottom: Spacing.xs,
  },
  sectionText: {
    fontSize: FontSizes.md,
    color: isDark ? Colors.dark.textSecondary : Colors.light.textSecondary,
    lineHeight: 22,
  },
  affirmationText: {
    fontSize: FontSizes.md,
    color: Colors.primary.purple,
    fontWeight: '500',
    lineHeight: 22,
    fontStyle: 'italic',
  },
  keywordsContainer: {
    marginBottom: Spacing.xl,
  },
  keywordsTitle: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: isDark ? Colors.dark.text : Colors.light.text,
    marginBottom: Spacing.sm,
  },
  keywordsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  keywordTag: {
    backgroundColor: Colors.primary.purple + '20',
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
    marginRight: Spacing.xs,
    marginBottom: Spacing.xs,
  },
  keywordText: {
    fontSize: FontSizes.sm,
    color: Colors.primary.purple,
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Spacing.lg,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: Spacing.xs,
  },
});
