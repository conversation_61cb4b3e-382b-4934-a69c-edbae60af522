import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Animated,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useAppSelector, useAppDispatch } from '../../store';
import { startReading, selectCard, endReading, setQuestion } from '../../store/slices/divinationSlice';
import { Colors } from '../../constants/colors';
import { Spacing, FontSizes, BorderRadius, CardSizes } from '../../constants/dimensions';
import { Button, Card, Input, MysticalLoading } from '../../components/common';
import { getAllSpreads, drawCardsForSpread, generateReading, TarotSpread } from '../../utils/cardUtils';
import { TarotCard } from '../../types';

const { width: screenWidth } = Dimensions.get('window');

export default function TarotReadingScreen({ navigation }: any) {
  const theme = useAppSelector(state => state.app.theme);
  const isDark = theme === 'dark';
  const dispatch = useAppDispatch();

  const [step, setStep] = useState<'select-spread' | 'ask-question' | 'draw-cards' | 'reveal-cards' | 'interpretation'>('select-spread');
  const [selectedSpread, setSelectedSpread] = useState<TarotSpread | null>(null);
  const [question, setQuestionText] = useState('');
  const [drawnCards, setDrawnCards] = useState<TarotCard[]>([]);
  const [revealedCards, setRevealedCards] = useState<boolean[]>([]);
  const [isDrawing, setIsDrawing] = useState(false);
  const [interpretation, setInterpretation] = useState<any>(null);

  const spreads = getAllSpreads();
  const styles = createStyles(isDark);

  const handleSpreadSelect = (spread: TarotSpread) => {
    setSelectedSpread(spread);
    setStep('ask-question');
    dispatch(startReading({ type: 'tarot', spread: spread.id }));
  };

  const handleQuestionSubmit = () => {
    if (question.trim()) {
      dispatch(setQuestion(question));
      setStep('draw-cards');
    }
  };

  const handleDrawCards = async () => {
    if (!selectedSpread) return;

    setIsDrawing(true);

    // 模拟抽牌过程
    setTimeout(() => {
      const cards = drawCardsForSpread(selectedSpread.id);
      setDrawnCards(cards);
      setRevealedCards(new Array(cards.length).fill(false));
      setIsDrawing(false);
      setStep('reveal-cards');
    }, 2000);
  };

  const handleCardReveal = (index: number) => {
    const newRevealed = [...revealedCards];
    newRevealed[index] = true;
    setRevealedCards(newRevealed);

    // 如果所有卡片都已翻开，生成解读
    if (newRevealed.every(revealed => revealed)) {
      setTimeout(() => {
        const reading = generateReading(selectedSpread!.id, drawnCards);
        setInterpretation(reading);
        setStep('interpretation');
      }, 1000);
    }
  };

  const handleNewReading = () => {
    setStep('select-spread');
    setSelectedSpread(null);
    setQuestionText('');
    setDrawnCards([]);
    setRevealedCards([]);
    setInterpretation(null);
    dispatch(endReading());
  };

  const renderSpreadSelection = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>选择塔罗牌阵</Text>
      <Text style={styles.stepSubtitle}>选择适合你问题的牌阵类型</Text>

      <ScrollView style={styles.spreadsContainer} showsVerticalScrollIndicator={false}>
        {spreads.map((spread) => (
          <TouchableOpacity
            key={spread.id}
            style={styles.spreadCard}
            onPress={() => handleSpreadSelect(spread)}
            activeOpacity={0.8}
          >
            <LinearGradient
              colors={Colors.gradients.mystical}
              style={styles.spreadGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <View style={styles.spreadHeader}>
                <Text style={styles.spreadName}>{spread.name}</Text>
                <Text style={styles.spreadCardCount}>{spread.cardCount}张牌</Text>
              </View>
              <Text style={styles.spreadDescription}>{spread.description}</Text>
            </LinearGradient>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderQuestionInput = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>提出你的问题</Text>
      <Text style={styles.stepSubtitle}>
        专注于你想要了解的问题，诚心诚意地提问
      </Text>

      <View style={styles.questionContainer}>
        <Input
          label="你的问题"
          placeholder="请输入你想要占卜的问题..."
          value={question}
          onChangeText={setQuestionText}
          multiline
          numberOfLines={4}
          style={styles.questionInput}
          theme={theme}
        />

        <Button
          title="开始抽牌"
          onPress={handleQuestionSubmit}
          disabled={!question.trim()}
          variant="gradient"
          gradient={Colors.gradients.mystical}
          style={styles.actionButton}
        />
      </View>
    </View>
  );

  const renderCardDrawing = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>抽取塔罗牌</Text>
      <Text style={styles.stepSubtitle}>
        静心冥想你的问题，然后点击抽牌
      </Text>

      <View style={styles.drawingContainer}>
        {isDrawing ? (
          <MysticalLoading
            visible={true}
            text="正在为你抽取塔罗牌..."
            size="large"
            theme={theme}
          />
        ) : (
          <Button
            title={`抽取 ${selectedSpread?.cardCount} 张牌`}
            onPress={handleDrawCards}
            variant="gradient"
            gradient={Colors.gradients.cosmic}
            size="large"
            style={styles.drawButton}
            icon="sparkles"
          />
        )}
      </View>
    </View>
  );

  const renderCardReveal = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>翻开你的塔罗牌</Text>
      <Text style={styles.stepSubtitle}>
        点击卡牌来揭示你的命运
      </Text>

      <View style={styles.cardsContainer}>
        {drawnCards.map((card, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.cardPosition,
              {
                left: selectedSpread?.positions[index].x * (screenWidth - CardSizes.tarot.width) || 0,
                top: selectedSpread?.positions[index].y * 200 || 0,
              }
            ]}
            onPress={() => !revealedCards[index] && handleCardReveal(index)}
            activeOpacity={0.8}
          >
            {revealedCards[index] ? (
              <View style={styles.revealedCard}>
                <LinearGradient
                  colors={Colors.gradients.golden}
                  style={styles.cardGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <Text style={styles.cardName}>{card.name}</Text>
                  <Text style={styles.cardEnglishName}>{card.englishName}</Text>
                </LinearGradient>
              </View>
            ) : (
              <View style={styles.hiddenCard}>
                <LinearGradient
                  colors={Colors.gradients.cosmic}
                  style={styles.cardGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <Ionicons name="sparkles" size={32} color={Colors.neutral.white} />
                  <Text style={styles.tapToReveal}>点击翻开</Text>
                </LinearGradient>
              </View>
            )}
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderInterpretation = () => (
    <ScrollView style={styles.stepContainer} showsVerticalScrollIndicator={false}>
      <Text style={styles.stepTitle}>占卜解读</Text>

      <Card style={styles.interpretationCard} theme={theme}>
        <Text style={styles.questionTitle}>你的问题：</Text>
        <Text style={styles.questionText}>{question}</Text>
      </Card>

      <Card style={styles.interpretationCard} theme={theme}>
        <Text style={styles.interpretationTitle}>牌阵解读：</Text>
        <Text style={styles.interpretationText}>{interpretation?.interpretation}</Text>
      </Card>

      <Card style={styles.interpretationCard} theme={theme}>
        <Text style={styles.summaryTitle}>总结建议：</Text>
        <Text style={styles.summaryText}>{interpretation?.summary}</Text>
      </Card>

      <View style={styles.actionButtons}>
        <Button
          title="重新占卜"
          onPress={handleNewReading}
          variant="outline"
          style={styles.actionButton}
        />
        <Button
          title="保存记录"
          onPress={() => {}}
          variant="gradient"
          gradient={Colors.gradients.mystical}
          style={styles.actionButton}
        />
      </View>
    </ScrollView>
  );

  const renderCurrentStep = () => {
    switch (step) {
      case 'select-spread':
        return renderSpreadSelection();
      case 'ask-question':
        return renderQuestionInput();
      case 'draw-cards':
        return renderCardDrawing();
      case 'reveal-cards':
        return renderCardReveal();
      case 'interpretation':
        return renderInterpretation();
      default:
        return renderSpreadSelection();
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons
            name="chevron-back"
            size={24}
            color={isDark ? Colors.dark.text : Colors.light.text}
          />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>塔罗占卜</Text>
        <View style={styles.placeholder} />
      </View>

      {renderCurrentStep()}
    </SafeAreaView>
  );
}

const createStyles = (isDark: boolean) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: isDark ? Colors.dark.background : Colors.light.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.screenPadding,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: isDark ? Colors.dark.border : Colors.light.border,
  },
  backButton: {
    padding: Spacing.xs,
  },
  headerTitle: {
    fontSize: FontSizes.lg,
    fontWeight: '600',
    color: isDark ? Colors.dark.text : Colors.light.text,
  },
  placeholder: {
    width: 40,
  },
  stepContainer: {
    flex: 1,
    paddingHorizontal: Spacing.screenPadding,
    paddingVertical: Spacing.lg,
  },
  stepTitle: {
    fontSize: FontSizes.xxl,
    fontWeight: '700',
    color: isDark ? Colors.dark.text : Colors.light.text,
    textAlign: 'center',
    marginBottom: Spacing.sm,
  },
  stepSubtitle: {
    fontSize: FontSizes.md,
    color: isDark ? Colors.dark.textSecondary : Colors.light.textSecondary,
    textAlign: 'center',
    marginBottom: Spacing.xl,
    lineHeight: 22,
  },
  spreadsContainer: {
    flex: 1,
  },
  spreadCard: {
    marginBottom: Spacing.md,
    borderRadius: BorderRadius.card,
    overflow: 'hidden',
  },
  spreadGradient: {
    padding: Spacing.md,
  },
  spreadHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  spreadName: {
    fontSize: FontSizes.lg,
    fontWeight: '600',
    color: Colors.neutral.white,
  },
  spreadCardCount: {
    fontSize: FontSizes.sm,
    color: Colors.neutral.white,
    opacity: 0.9,
  },
  spreadDescription: {
    fontSize: FontSizes.md,
    color: Colors.neutral.white,
    opacity: 0.9,
    lineHeight: 20,
  },
  questionContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  questionInput: {
    marginBottom: Spacing.xl,
  },
  actionButton: {
    marginHorizontal: Spacing.sm,
  },
  drawingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  drawButton: {
    paddingHorizontal: Spacing.xl,
  },
  cardsContainer: {
    flex: 1,
    position: 'relative',
    marginTop: Spacing.xl,
  },
  cardPosition: {
    position: 'absolute',
    width: CardSizes.tarot.width,
    height: CardSizes.tarot.height,
  },
  revealedCard: {
    width: '100%',
    height: '100%',
    borderRadius: BorderRadius.card,
    overflow: 'hidden',
  },
  hiddenCard: {
    width: '100%',
    height: '100%',
    borderRadius: BorderRadius.card,
    overflow: 'hidden',
  },
  cardGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.sm,
  },
  cardName: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: Colors.neutral.white,
    textAlign: 'center',
    marginBottom: Spacing.xs,
  },
  cardEnglishName: {
    fontSize: FontSizes.sm,
    color: Colors.neutral.white,
    opacity: 0.9,
    textAlign: 'center',
  },
  tapToReveal: {
    fontSize: FontSizes.sm,
    color: Colors.neutral.white,
    opacity: 0.9,
    marginTop: Spacing.xs,
  },
  interpretationCard: {
    marginBottom: Spacing.md,
  },
  questionTitle: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: isDark ? Colors.dark.text : Colors.light.text,
    marginBottom: Spacing.xs,
  },
  questionText: {
    fontSize: FontSizes.md,
    color: isDark ? Colors.dark.textSecondary : Colors.light.textSecondary,
    lineHeight: 22,
  },
  interpretationTitle: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: isDark ? Colors.dark.text : Colors.light.text,
    marginBottom: Spacing.xs,
  },
  interpretationText: {
    fontSize: FontSizes.md,
    color: isDark ? Colors.dark.textSecondary : Colors.light.textSecondary,
    lineHeight: 22,
  },
  summaryTitle: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: isDark ? Colors.dark.text : Colors.light.text,
    marginBottom: Spacing.xs,
  },
  summaryText: {
    fontSize: FontSizes.md,
    color: isDark ? Colors.dark.textSecondary : Colors.light.textSecondary,
    lineHeight: 22,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: Spacing.xl,
    marginBottom: Spacing.lg,
  },
});
