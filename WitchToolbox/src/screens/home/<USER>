import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useAppSelector } from '../../store';
import { Colors } from '../../constants/colors';
import { Spacing, FontSizes, BorderRadius } from '../../constants/dimensions';

export default function HomeScreen({ navigation }: any) {
  const theme = useAppSelector(state => state.app.theme);
  const user = useAppSelector(state => state.user.currentUser);
  const isDark = theme === 'dark';

  const styles = createStyles(isDark);

  const quickActions = [
    {
      id: 'tarot',
      title: '塔罗占卜',
      subtitle: '探索内心的智慧',
      icon: 'sparkles',
      gradient: Colors.gradients.mystical,
      onPress: () => navigation.navigate('TarotReading'),
    },
    {
      id: 'oracle',
      title: '神谕卡',
      subtitle: '接收天使的指引',
      icon: 'star',
      gradient: Colors.gradients.golden,
      onPress: () => navigation.navigate('OracleReading'),
    },
    {
      id: 'daily',
      title: '每日一卡',
      subtitle: '今日的能量指引',
      icon: 'sunny',
      gradient: Colors.gradients.aurora,
      onPress: () => {},
    },
    {
      id: 'learning',
      title: '学习课程',
      subtitle: '提升占卜技能',
      icon: 'school',
      gradient: Colors.gradients.sunset,
      onPress: () => navigation.navigate('Learning'),
    },
  ];

  const renderQuickAction = (action: any) => (
    <TouchableOpacity
      key={action.id}
      style={styles.actionCard}
      onPress={action.onPress}
      activeOpacity={0.8}
    >
      <LinearGradient
        colors={action.gradient}
        style={styles.actionGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.actionContent}>
          <Ionicons 
            name={action.icon} 
            size={32} 
            color={Colors.neutral.white} 
          />
          <Text style={styles.actionTitle}>{action.title}</Text>
          <Text style={styles.actionSubtitle}>{action.subtitle}</Text>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar 
        barStyle={isDark ? 'light-content' : 'dark-content'} 
        backgroundColor={isDark ? Colors.dark.background : Colors.light.background}
      />
      
      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <LinearGradient
            colors={isDark ? Colors.gradients.cosmic : Colors.gradients.mystical}
            style={styles.headerGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <View style={styles.headerContent}>
              <View>
                <Text style={styles.greeting}>
                  {getGreeting()}
                </Text>
                <Text style={styles.username}>
                  {user?.username || '神秘的探索者'}
                </Text>
              </View>
              <TouchableOpacity style={styles.profileButton}>
                <Ionicons 
                  name="person-circle" 
                  size={40} 
                  color={Colors.neutral.white} 
                />
              </TouchableOpacity>
            </View>
          </LinearGradient>
        </View>

        {/* Daily Oracle Card */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>今日神谕</Text>
          <TouchableOpacity style={styles.dailyCard}>
            <LinearGradient
              colors={Colors.gradients.aurora}
              style={styles.dailyCardGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <View style={styles.dailyCardContent}>
                <Ionicons 
                  name="star-outline" 
                  size={48} 
                  color={Colors.neutral.white} 
                />
                <Text style={styles.dailyCardTitle}>天使的祝福</Text>
                <Text style={styles.dailyCardMessage}>
                  今天是充满可能性的一天，相信你内在的智慧，它会指引你走向正确的方向。
                </Text>
                <Text style={styles.dailyCardAction}>点击查看详情</Text>
              </View>
            </LinearGradient>
          </TouchableOpacity>
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>快速开始</Text>
          <View style={styles.actionsGrid}>
            {quickActions.map(renderQuickAction)}
          </View>
        </View>

        {/* Learning Progress */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>学习进度</Text>
          <View style={styles.progressCard}>
            <View style={styles.progressHeader}>
              <Text style={styles.progressTitle}>塔罗基础课程</Text>
              <Text style={styles.progressPercentage}>75%</Text>
            </View>
            <View style={styles.progressBar}>
              <View style={[styles.progressFill, { width: '75%' }]} />
            </View>
            <Text style={styles.progressText}>
              已完成 15/20 课时 • 连续学习 7 天
            </Text>
          </View>
        </View>

        {/* Recent Readings */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>最近占卜</Text>
            <TouchableOpacity>
              <Text style={styles.seeAllText}>查看全部</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.readingsList}>
            <TouchableOpacity style={styles.readingItem}>
              <View style={styles.readingIcon}>
                <Ionicons 
                  name="sparkles" 
                  size={20} 
                  color={Colors.primary.purple} 
                />
              </View>
              <View style={styles.readingContent}>
                <Text style={styles.readingTitle}>爱情三角牌阵</Text>
                <Text style={styles.readingDate}>2小时前</Text>
              </View>
              <Ionicons 
                name="chevron-forward" 
                size={16} 
                color={isDark ? Colors.dark.textSecondary : Colors.light.textSecondary} 
              />
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.readingItem}>
              <View style={styles.readingIcon}>
                <Ionicons 
                  name="star" 
                  size={20} 
                  color={Colors.secondary.emerald} 
                />
              </View>
              <View style={styles.readingContent}>
                <Text style={styles.readingTitle}>天使指引卡</Text>
                <Text style={styles.readingDate}>昨天</Text>
              </View>
              <Ionicons 
                name="chevron-forward" 
                size={16} 
                color={isDark ? Colors.dark.textSecondary : Colors.light.textSecondary} 
              />
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

function getGreeting(): string {
  const hour = new Date().getHours();
  if (hour < 6) return '夜深了';
  if (hour < 12) return '早上好';
  if (hour < 18) return '下午好';
  return '晚上好';
}

const createStyles = (isDark: boolean) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: isDark ? Colors.dark.background : Colors.light.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    marginBottom: Spacing.lg,
  },
  headerGradient: {
    paddingTop: Spacing.xl,
    paddingBottom: Spacing.lg,
    paddingHorizontal: Spacing.screenPadding,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  greeting: {
    fontSize: FontSizes.md,
    color: Colors.neutral.white,
    opacity: 0.9,
  },
  username: {
    fontSize: FontSizes.xl,
    fontWeight: '600',
    color: Colors.neutral.white,
    marginTop: 4,
  },
  profileButton: {
    padding: 4,
  },
  section: {
    marginBottom: Spacing.xl,
    paddingHorizontal: Spacing.screenPadding,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  sectionTitle: {
    fontSize: FontSizes.lg,
    fontWeight: '600',
    color: isDark ? Colors.dark.text : Colors.light.text,
    marginBottom: Spacing.md,
  },
  seeAllText: {
    fontSize: FontSizes.sm,
    color: Colors.primary.purple,
    fontWeight: '500',
  },
  dailyCard: {
    borderRadius: BorderRadius.card,
    overflow: 'hidden',
  },
  dailyCardGradient: {
    padding: Spacing.lg,
  },
  dailyCardContent: {
    alignItems: 'center',
  },
  dailyCardTitle: {
    fontSize: FontSizes.lg,
    fontWeight: '600',
    color: Colors.neutral.white,
    marginTop: Spacing.sm,
    marginBottom: Spacing.sm,
  },
  dailyCardMessage: {
    fontSize: FontSizes.md,
    color: Colors.neutral.white,
    textAlign: 'center',
    lineHeight: 22,
    opacity: 0.9,
    marginBottom: Spacing.sm,
  },
  dailyCardAction: {
    fontSize: FontSizes.sm,
    color: Colors.neutral.white,
    fontWeight: '500',
    opacity: 0.8,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionCard: {
    width: '48%',
    marginBottom: Spacing.md,
    borderRadius: BorderRadius.card,
    overflow: 'hidden',
  },
  actionGradient: {
    padding: Spacing.md,
    minHeight: 120,
  },
  actionContent: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  actionTitle: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: Colors.neutral.white,
    marginTop: Spacing.sm,
  },
  actionSubtitle: {
    fontSize: FontSizes.sm,
    color: Colors.neutral.white,
    opacity: 0.8,
    marginTop: 2,
    textAlign: 'center',
  },
  progressCard: {
    backgroundColor: isDark ? Colors.dark.card : Colors.light.card,
    padding: Spacing.md,
    borderRadius: BorderRadius.card,
    borderWidth: 1,
    borderColor: isDark ? Colors.dark.border : Colors.light.border,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  progressTitle: {
    fontSize: FontSizes.md,
    fontWeight: '500',
    color: isDark ? Colors.dark.text : Colors.light.text,
  },
  progressPercentage: {
    fontSize: FontSizes.md,
    fontWeight: '600',
    color: Colors.primary.purple,
  },
  progressBar: {
    height: 6,
    backgroundColor: isDark ? Colors.dark.border : Colors.light.border,
    borderRadius: 3,
    marginBottom: Spacing.sm,
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.primary.purple,
    borderRadius: 3,
  },
  progressText: {
    fontSize: FontSizes.sm,
    color: isDark ? Colors.dark.textSecondary : Colors.light.textSecondary,
  },
  readingsList: {
    backgroundColor: isDark ? Colors.dark.card : Colors.light.card,
    borderRadius: BorderRadius.card,
    borderWidth: 1,
    borderColor: isDark ? Colors.dark.border : Colors.light.border,
  },
  readingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: isDark ? Colors.dark.border : Colors.light.border,
  },
  readingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: isDark ? Colors.dark.background : Colors.light.background,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.sm,
  },
  readingContent: {
    flex: 1,
  },
  readingTitle: {
    fontSize: FontSizes.md,
    fontWeight: '500',
    color: isDark ? Colors.dark.text : Colors.light.text,
  },
  readingDate: {
    fontSize: FontSizes.sm,
    color: isDark ? Colors.dark.textSecondary : Colors.light.textSecondary,
    marginTop: 2,
  },
});
