{"name": "react-native-reanimated", "version": "4.0.1", "description": "More powerful alternative to Animated library for React Native.", "keywords": ["react-native", "react", "native", "reanimated"], "scripts": {"test": "jest", "lint": "yarn lint:js && yarn lint:common && yarn lint:android && yarn lint:apple", "lint:js": "eslint src __tests__ __typetests__ && yarn prettier --check src __tests__ __typetests__", "lint:android": "../../scripts/validate-android.sh && ./android/gradlew -p android spotlessCheck -q && ../../scripts/cpplint.sh android/src && yarn format:android:cpp --dry-run -Werror && yarn lint:cmake", "lint:common": "../../scripts/validate-common.sh && ../../scripts/cpplint.sh Common && yarn format:common --dry-run -Werror", "lint:clang-tidy": "find Common -iname \"*.h\" -o -iname \"*.cpp\" | xargs ../../scripts/clang-tidy-lint.sh", "lint:apple": "../../scripts/validate-apple.sh && yarn format:apple --dry-run -Werror", "lint:cmake": "find ./android -type d \\( -name build -o -name .cxx \\) -prune -o -type f -name 'CMakeLists.txt' -print | xargs ../../scripts/lint-cmake.sh", "format": "yarn format:js && yarn format:apple && yarn format:android && yarn format:common", "format:js": "prettier --write --list-different src __tests__ __typetests__", "format:apple": "find apple -iname \"*.h\" -o -iname \"*.m\" -o -iname \"*.mm\" -o -iname \"*.cpp\" | xargs clang-format -i", "format:android": "yarn format:android:java && yarn format:android:cpp && yarn format:android:cmake", "format:android:java": "node ../../scripts/format-java.js", "format:android:cpp": "find android/src -iname \"*.h\" -o -iname \"*.cpp\" | xargs clang-format -i", "format:android:cmake": "find ./android -type d \\( -name build -o -name .cxx \\) -prune -o -type f -name 'CMakeLists.txt' -print | xargs ../../scripts/format-cmake.sh", "format:common": "find Common -iname \"*.h\" -o -iname \"*.cpp\" | xargs clang-format -i", "find-unused-code:js": "knip", "type:check": "yarn type:check:src && yarn type:check:app && ./scripts/test-ts.sh __typetests__/common", "type:check:src": "yarn tsc --noEmit", "type:check:app": "yarn workspace common-app type:check", "type:check:tests:common": "./scripts/test-ts.sh __typetests__/common", "build": "yarn workspace react-native-worklets build && bob build", "circular-dependency-check": "yarn madge --extensions js,jsx --circular lib", "use-strict-check": "node ../../scripts/validate-use-strict.js", "prepack": "cp ../../README.md ./README.md", "postpack": "rm ./README.md"}, "main": "lib/module/index", "module": "lib/module/index", "react-native": "src/index", "source": "src/index", "types": "lib/typescript/index.d.ts", "files": ["Common/", "src/", "lib/", "android/src/main/AndroidManifest.xml", "android/src/main/java/", "android/build.gradle", "android/", "apple/", "RNReanimated.podspec", "scripts/reanimated_utils.rb", "scripts/validate-worklets-build.js", "scripts/validate-worklets-version.js", "scripts/worklets-version.json", "mock.js", "plugin/index.js", "metro-config", "!**/__tests__", "!**/__fixtures__", "!**/__mocks__", "!apple/build/", "!android/build/", "!android/.cxx/", "!android/.gradle/", "!__snapshots__", "!*.test.js", "!*.test.js.map", "!**/node_modules"], "repository": {"type": "git", "url": "git+https://github.com/software-mansion/react-native-reanimated.git", "directory": "packages/react-native-reanimated"}, "author": {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/software-mansion/react-native-reanimated/issues"}, "homepage": "https://docs.swmansion.com/react-native-reanimated", "dependencies": {"react-native-is-edge-to-edge": "^1.2.1", "semver": "7.7.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0", "react": "*", "react-native": "*", "react-native-worklets": ">=0.3.0"}, "devDependencies": {"@babel/cli": "^7.20.0", "@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/types": "^7.20.0", "@react-native/babel-preset": "0.80.0", "@react-native/eslint-config": "0.80.0", "@react-native/metro-config": "0.80.0", "@react-native/typescript-config": "0.80.0", "@testing-library/jest-native": "^4.0.4", "@testing-library/react-hooks": "^8.0.1", "@testing-library/react-native": "^13.0.1", "@types/babel__core": "^7.20.0", "@types/babel__generator": "^7.6.4", "@types/babel__traverse": "^7.14.2", "@types/convert-source-map": "^2.0.0", "@types/jest": "^29.5.13", "@types/node": "^18.0.0", "@types/react": "^19.1.0", "@types/react-test-renderer": "^19.1.0", "@types/semver": "^7", "@typescript-eslint/parser": "^6.19.0", "@typescript-eslint/rule-tester": "^6.21.0", "axios": "^1.8.2", "babel-eslint": "^10.1.0", "babel-plugin-module-resolver": "^5.0.0", "clang-format-node": "^1.3.1", "code-tag": "^1.1.0", "cspell": "^8.8.0", "eslint": "^9.29.0", "eslint-config-standard": "^17.1.0", "eslint-import-resolver-babel-module": "^5.3.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.13.0", "eslint-plugin-n": "^17.19.0", "eslint-plugin-no-inline-styles": "^1.0.5", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-reanimated": "workspace:*", "eslint-plugin-standard": "^5.0.0", "eslint-plugin-tsdoc": "^0.4.0", "jest": "^29.0.0", "knip": "^5.61.3", "madge": "^5.0.1", "prettier": "^3.3.3", "react": "19.1.0", "react-native": "patch:react-native@npm%3A0.80.0#~/.yarn/patches/react-native-npm-0.80.0-dababd395b.patch", "react-native-builder-bob": "patch:react-native-builder-bob@npm%3A0.33.1#~/.yarn/patches/react-native-builder-bob-npm-0.33.1-383d9e23a5.patch", "react-native-gesture-handler": "2.26.0", "react-native-web": "0.20.0", "react-native-worklets": "workspace:*", "react-test-renderer": "19.1.0", "shelljs": "^0.8.5", "typescript": "~5.3.0"}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": [["module", {"esm": true, "jsxRuntime": "classic"}], "typescript"]}, "codegenConfig": {"name": "rnreanimated", "type": "modules", "jsSrcsDir": "./src/specs", "android": {"javaPackageName": "com.swmansion.reanimated"}}, "sideEffects": ["./src/layoutReanimation/animationsManager.ts", "./lib/module/layoutReanimation/animationsManager.js", "./src/core.ts", "./lib/module/core.js", "./src/initializers.ts", "./lib/module/initializers.js", "./src/index.ts", "./lib/module/index.js"]}